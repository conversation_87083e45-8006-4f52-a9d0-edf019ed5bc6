'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  DollarSign, 
  Zap, 
  Users, 
  Trophy, 
  Car, 
  Leaf, 
  ArrowRight,
  TrendingUp,
  Shield,
  Gauge
} from 'lucide-react'
import Link from 'next/link'

interface QuickFilter {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  badge?: string
  filters: Record<string, string>
  color: string
  stats?: string
}

export function QuickFiltersSection() {
  const quickFilters: QuickFilter[] = [
    {
      id: 'budget-friendly',
      title: 'Budget Friendly',
      description: 'Quality EVs under $40k',
      icon: <DollarSign className="h-6 w-6" />,
      badge: 'Best Value',
      filters: { priceMax: '4000000', productionStatus: 'current' },
      color: 'bg-green-500',
      stats: '15+ models'
    },
    {
      id: 'long-range',
      title: 'Long Range',
      description: 'EVs with 300+ miles range',
      icon: <Zap className="h-6 w-6" />,
      badge: 'Road Trip Ready',
      filters: { rangeMin: '300', productionStatus: 'current' },
      color: 'bg-blue-500',
      stats: '25+ models'
    },
    {
      id: 'family-suvs',
      title: 'Family SUVs',
      description: 'Spacious SUVs & crossovers',
      icon: <Users className="h-6 w-6" />,
      badge: 'Family Friendly',
      filters: { bodyType: 'suv', seatingCapacity: '5', productionStatus: 'current' },
      color: 'bg-purple-500',
      stats: '20+ models'
    },
    {
      id: 'performance',
      title: 'Performance',
      description: 'Fast acceleration & top speed',
      icon: <Gauge className="h-6 w-6" />,
      badge: 'High Performance',
      filters: { accelerationMax: '4', productionStatus: 'current' },
      color: 'bg-red-500',
      stats: '12+ models'
    },
    {
      id: 'luxury',
      title: 'Luxury EVs',
      description: 'Premium electric vehicles',
      icon: <Trophy className="h-6 w-6" />,
      badge: 'Premium',
      filters: { priceMin: '7000000', productionStatus: 'current' },
      color: 'bg-amber-500',
      stats: '18+ models'
    },
    {
      id: 'eco-friendly',
      title: 'Most Efficient',
      description: 'Highest efficiency ratings',
      icon: <Leaf className="h-6 w-6" />,
      badge: 'Eco Champion',
      filters: { sortBy: 'efficiency_mpge', sortOrder: 'desc', productionStatus: 'current' },
      color: 'bg-emerald-500',
      stats: '30+ models'
    }
  ]

  const buildFilterUrl = (filters: Record<string, string>) => {
    const params = new URLSearchParams(filters)
    return `/ev-models?${params.toString()}`
  }

  return (
    <section className="bg-white py-16 dark:bg-gray-900">
      <div className="container">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <Badge variant="secondary" className="mb-4">
            <TrendingUp className="mr-1 h-3 w-3" />
            Quick Filters
          </Badge>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl">
            Find EVs by Category
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            Skip the search and jump straight to what you're looking for with our curated EV categories
          </p>
        </div>

        {/* Quick Filters Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {quickFilters.map((filter) => (
            <Card key={filter.id} className="group overflow-hidden border-0 shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className={`rounded-lg p-3 ${filter.color} text-white`}>
                    {filter.icon}
                  </div>
                  {filter.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {filter.badge}
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-xl">{filter.title}</CardTitle>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {filter.description}
                </p>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="mb-4 flex items-center justify-between">
                  {filter.stats && (
                    <span className="text-sm font-medium text-electric-600">
                      {filter.stats}
                    </span>
                  )}
                </div>
                
                <Link href={buildFilterUrl(filter.filters)}>
                  <Button className="w-full group">
                    Explore {filter.title}
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Advanced Filters CTA */}
        <div className="mt-12 text-center">
          <Card className="mx-auto max-w-2xl border-electric-200 bg-electric-50 dark:border-electric-800 dark:bg-electric-950">
            <CardContent className="p-8">
              <div className="mb-4">
                <Shield className="mx-auto h-12 w-12 text-electric-600" />
              </div>
              <h3 className="mb-2 text-xl font-semibold text-electric-900 dark:text-electric-100">
                Need More Specific Filters?
              </h3>
              <p className="mb-6 text-electric-800 dark:text-electric-200">
                Use our advanced filtering system to find EVs that match your exact requirements
              </p>
              <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
                <Link href="/ev-models">
                  <Button variant="outline" className="group">
                    Advanced Filters
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
                <Link href="/compare">
                  <Button className="bg-electric-600 hover:bg-electric-700 group">
                    Compare EVs
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
