import pLimit from "p-limit";
import pRetry from "p-retry";
import { BrowserManager } from "./browser";
import { EVDataParser } from "./parser";
import { OutputManager } from "../utils/output";
import { validateEVModel, logValidationResults } from "../utils/validation";
import { ProgressLogger, logger } from "../utils/logger";
import {
  ScrapingConfig,
  EVModel,
  ScrapingResult,
  ScrapingError,
} from "../types";
import { siteConfig } from "../utils/config";

/**
 * Main EV scraper class
 */
export class EVScraper {
  private config: ScrapingConfig;
  private browserManager: BrowserManager;
  private outputManager: OutputManager;
  private progressLogger?: ProgressLogger;
  private scrapedData: EVModel[] = [];
  private errors: ScrapingError[] = [];

  constructor(config: ScrapingConfig) {
    this.config = config;
    this.browserManager = new BrowserManager(config);
    this.outputManager = new OutputManager(config.output_directory);
  }

  /**
   * Main scraping method
   */
  async scrape(): Promise<ScrapingResult> {
    const startTime = Date.now();

    try {
      logger.info("Starting EV scraping session", {
        config: this.config,
        timestamp: new Date().toISOString(),
      });

      // Initialize browser
      await this.browserManager.initialize();

      // Get all car URLs to scrape
      const carUrls = await this.discoverCarUrls();

      if (carUrls.length === 0) {
        throw new Error("No car URLs found to scrape");
      }

      // Initialize progress tracking
      this.progressLogger = new ProgressLogger(carUrls.length);

      // Scrape all cars with concurrency control
      await this.scrapeAllCars(carUrls);

      // Save results
      const outputFiles = await this.saveResults();

      // Generate final result
      const result: ScrapingResult = {
        success: true,
        total_vehicles: carUrls.length,
        successful_scrapes: this.scrapedData.length,
        failed_scrapes: this.errors.length,
        errors: this.errors,
        execution_time_ms: Date.now() - startTime,
        output_files: outputFiles,
      };

      this.progressLogger.logCompletion();
      logger.info("Scraping session completed successfully", result);

      return result;
    } catch (error) {
      const result: ScrapingResult = {
        success: false,
        total_vehicles: 0,
        successful_scrapes: this.scrapedData.length,
        failed_scrapes: this.errors.length,
        errors: this.errors,
        execution_time_ms: Date.now() - startTime,
        output_files: [],
      };

      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Scraping session failed", {
        error: errorMessage,
        result,
      });

      return result;
    } finally {
      await this.browserManager.close();
    }
  }

  /**
   * Discover all car URLs to scrape
   */
  private async discoverCarUrls(): Promise<string[]> {
    logger.info("Discovering car URLs");

    const page = await this.browserManager.createPage();
    const allUrls: string[] = [];

    try {
      // Start with the main car listing page
      const success = await this.browserManager.navigateToUrl(
        page,
        siteConfig.urls.carList
      );

      if (!success) {
        throw new Error("Failed to load car listing page");
      }

      // Extract car links from current page
      const html = await this.browserManager.extractPageContent(page);
      const parser = new EVDataParser(html);
      const carLinks = parser.extractCarLinks();

      allUrls.push(...carLinks);
      logger.info("Found car links on main page", { count: carLinks.length });

      // Look for pagination and scrape additional pages
      const additionalUrls = await this.scrapePaginatedPages(page);
      allUrls.push(...additionalUrls);

      // Remove duplicates and apply filters
      const uniqueUrls = [...new Set(allUrls)];
      const filteredUrls = this.applyUrlFilters(uniqueUrls);

      logger.info("Car URL discovery completed", {
        totalFound: uniqueUrls.length,
        afterFiltering: filteredUrls.length,
      });

      return filteredUrls;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to discover car URLs", { error: errorMessage });
      throw error;
    } finally {
      await page.close();
    }
  }

  /**
   * Scrape additional pages from pagination
   */
  private async scrapePaginatedPages(page: any): Promise<string[]> {
    const allUrls: string[] = [];
    let currentPage = 1;
    const maxPages = this.config.max_pages || 10;

    while (currentPage < maxPages) {
      try {
        // Look for next page link
        const nextPageExists = await this.browserManager.elementExists(
          page,
          siteConfig.selectors.nextPage
        );

        if (!nextPageExists) {
          logger.info("No more pages found", { currentPage });
          break;
        }

        // Click next page
        await page.click(siteConfig.selectors.nextPage);
        await page
          .waitForLoadState("networkidle", { timeout: 10000 })
          .catch(() => {});

        currentPage++;
        logger.debug("Navigated to page", { currentPage });

        // Extract links from this page
        const html = await this.browserManager.extractPageContent(page);
        const parser = new EVDataParser(html);
        const carLinks = parser.extractCarLinks();

        allUrls.push(...carLinks);
        logger.debug("Found car links on page", {
          page: currentPage,
          count: carLinks.length,
        });

        // Rate limiting between pages
        await this.delay(this.config.delay_between_requests);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        logger.warn("Failed to scrape page", {
          page: currentPage,
          error: errorMessage,
        });
        break;
      }
    }

    return allUrls;
  }

  /**
   * Apply filters to discovered URLs
   */
  private applyUrlFilters(urls: string[]): string[] {
    let filtered = urls;

    // Apply make filter if specified
    if (this.config.specific_makes && this.config.specific_makes.length > 0) {
      filtered = filtered.filter((url) => {
        const urlLower = url.toLowerCase();
        return this.config.specific_makes!.some((make) =>
          urlLower.includes(make.toLowerCase())
        );
      });
    }

    // Apply max pages limit
    if (this.config.max_pages) {
      filtered = filtered.slice(0, this.config.max_pages);
    }

    return filtered;
  }

  /**
   * Scrape all car URLs with concurrency control
   */
  private async scrapeAllCars(urls: string[]): Promise<void> {
    const limit = pLimit(this.config.max_concurrent_requests);

    const scrapingPromises = urls.map((url) =>
      limit(() => this.scrapeCarWithRetry(url))
    );

    await Promise.allSettled(scrapingPromises);
  }

  /**
   * Scrape a single car with retry logic
   */
  private async scrapeCarWithRetry(url: string): Promise<void> {
    try {
      await pRetry(() => this.scrapeSingleCar(url), {
        retries: this.config.max_retries,
        minTimeout: this.config.retry_delay,
        onFailedAttempt: (error) => {
          logger.warn("Retry attempt failed", {
            url,
            attempt: error.attemptNumber,
            retriesLeft: error.retriesLeft,
            error: error instanceof Error ? error.message : String(error),
          });
        },
      });
    } catch (error) {
      this.handleScrapingError(url, error, this.config.max_retries);
    }
  }

  /**
   * Scrape a single car page
   */
  private async scrapeSingleCar(url: string): Promise<void> {
    const page = await this.browserManager.createPage();

    try {
      // Navigate to car page
      const success = await this.browserManager.navigateToUrl(page, url);

      if (!success) {
        throw new Error("Failed to load car page");
      }

      // Extract page content
      const html = await this.browserManager.extractPageContent(page);

      if (!html || html.length < 1000) {
        throw new Error("Page content too short or empty");
      }

      // Parse EV data
      const parser = new EVDataParser(html);
      const evData = parser.extractEVModel(url);

      // Validate data
      const validation = validateEVModel(evData);
      logValidationResults(url, validation);

      if (validation.isValid && validation.cleanedData) {
        this.scrapedData.push(validation.cleanedData);
        this.progressLogger?.logProgress(url, true, {
          make: validation.cleanedData.make,
          model: validation.cleanedData.model,
        });
      } else {
        throw new Error(
          `Data validation failed: ${validation.errors.join(", ")}`
        );
      }
    } catch (error) {
      throw error; // Re-throw for retry logic
    } finally {
      await page.close();
    }
  }

  /**
   * Handle scraping errors
   */
  private handleScrapingError(
    url: string,
    error: any,
    retryCount: number
  ): void {
    const scrapingError: ScrapingError = {
      url,
      error_message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
      retry_count: retryCount,
    };

    this.errors.push(scrapingError);
    this.progressLogger?.logError(url, error, retryCount);
  }

  /**
   * Save all results to files
   */
  private async saveResults(): Promise<string[]> {
    const outputFiles: string[] = [];

    try {
      // Save scraped data
      if (this.scrapedData.length > 0) {
        if (this.config.save_json) {
          const jsonFile = await this.outputManager.saveAsJSON(
            this.scrapedData
          );
          outputFiles.push(jsonFile);
        }

        if (this.config.save_csv) {
          const csvFile = await this.outputManager.saveAsCSV(this.scrapedData);
          outputFiles.push(csvFile);
        }

        if (this.config.save_sql) {
          const sqlFile = await this.outputManager.saveAsSQL(this.scrapedData);
          outputFiles.push(sqlFile);
        }
      }

      // Save errors if any
      if (this.errors.length > 0) {
        const errorsFile = await this.outputManager.saveErrors(this.errors);
        outputFiles.push(errorsFile);
      }

      return outputFiles;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to save results", { error: errorMessage });
      throw error;
    }
  }

  /**
   * Utility delay function
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
