{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../../src/scraper/browser.ts"], "names": [], "mappings": ";;;AAAA,2CAAoE;AAEpE,4CAA2D;AAC3D,4CAA4C;AAE5C;;GAEG;AACH,MAAa,cAAc;IACjB,OAAO,GAAmB,IAAI,CAAA;IAC9B,OAAO,GAA0B,IAAI,CAAA;IACrC,MAAM,CAAgB;IAE9B,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,QAAQ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;aACzE,CAAC,CAAA;YAEF,IAAI,CAAC,OAAO,GAAG,MAAM,qBAAQ,CAAC,MAAM,CAAC;gBACnC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,IAAI,EAAE;oBACJ,cAAc;oBACd,0BAA0B;oBAC1B,yBAAyB;oBACzB,iCAAiC;oBACjC,gBAAgB;oBAChB,aAAa;oBACb,eAAe;iBAChB;aACF,CAAC,CAAA;YAEF,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC3C,QAAQ,EAAE;oBACR,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;oBACjC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe;iBACpC;gBACD,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;gBACjC,gBAAgB,EAAE,mBAAU,CAAC,OAAO;aACrC,CAAC,CAAA;YAEF,qDAAqD;YACrD,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;gBACzC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,YAAY,EAAE,CAAA;gBAEnD,mDAAmD;gBACnD,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBACpE,KAAK,CAAC,KAAK,EAAE,CAAA;gBACf,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,QAAQ,EAAE,CAAA;gBAClB,CAAC;YACH,CAAC,CAAC,CAAA;YAEF,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAA;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;YACtE,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAA;QACpD,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;QAEzC,8BAA8B;QAC9B,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE;YACzB,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;gBAC3B,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;YAChE,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,EAAE;YAC7B,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,OAAO,EAAE,EAAE;YACnC,eAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;gBAC7B,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;gBAClB,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,EAAE,SAAS;aACtC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,IAAU,EAAE,GAAW;QACzC,IAAI,CAAC;YACH,sBAAsB;YACtB,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;gBAC3C,IAAA,0BAAiB,EAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAA;gBACrD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAA;YACtD,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;YAE1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACpC,SAAS,EAAE,kBAAkB;gBAC7B,OAAO,EAAE,KAAK;aACf,CAAC,CAAA;YAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;gBAC5C,OAAO,KAAK,CAAA;YACd,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAA;YAChC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;gBAClB,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAA;gBACnD,OAAO,KAAK,CAAA;YACd,CAAC;YAED,mCAAmC;YACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACxE,eAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;YAClE,CAAC,CAAC,CAAA;YAEF,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAA;YAC9D,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,GAAG;gBACH,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;YACF,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,IAAU;QACjC,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;YACxE,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,IAAU,EAAE,QAAgB;QAC/C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,UAAU,CAAC;gBACpB,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;aACf,CAAC,CAAA;YACF,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,IAAU,EAAE,QAAgB;QAC9C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;YACtC,OAAO,OAAO,KAAK,IAAI,CAAA;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,IAAU,EAAE,QAAgB;QAC/C,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;YACtC,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,MAAM,OAAO,CAAC,WAAW,EAAE,CAAA;YACpC,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBACzC,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;YACF,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,IAAU,EAAE,QAAgB,EAAE,SAAiB;QACvE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;YACtC,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,MAAM,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;YAC9C,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBAC9C,QAAQ;gBACR,SAAS;gBACT,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;YACF,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,IAAU,EAAE,QAAgB;QACnD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAA;YACxC,MAAM,KAAK,GAAa,EAAE,CAAA;YAE1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE,CAAA;gBACxC,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;gBACzB,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAA;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBAC9C,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAA;YACF,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,IAAU;QACzB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;gBACvB,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;oBACnC,IAAI,WAAW,GAAG,CAAC,CAAA;oBACnB,MAAM,QAAQ,GAAG,GAAG,CAAA;oBACpB,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;wBAC7B,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAA;wBAC/C,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAA;wBAC5B,WAAW,IAAI,QAAQ,CAAA;wBAEvB,IAAI,WAAW,IAAI,YAAY,EAAE,CAAC;4BAChC,aAAa,CAAC,KAAK,CAAC,CAAA;4BACpB,OAAO,EAAE,CAAA;wBACX,CAAC;oBACH,CAAC,EAAE,GAAG,CAAC,CAAA;gBACT,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;gBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACrB,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;gBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACrB,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAA;IACvD,CAAC;CACF;AA9SD,wCA8SC"}