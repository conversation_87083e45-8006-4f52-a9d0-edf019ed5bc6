import { EVModel } from '../types';
/**
 * Data validation and cleaning utilities
 */
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    cleanedData?: EVModel;
}
/**
 * Validates and cleans scraped EV data
 */
export declare function validateEVModel(data: Partial<EVModel>): ValidationResult;
/**
 * Extracts numeric value from text using regex patterns
 */
export declare function extractNumericValue(text: string, pattern: RegExp): number | null;
/**
 * Cleans and normalizes text data
 */
export declare function cleanText(text: string): string;
/**
 * Converts various price formats to cents
 */
export declare function parsePrice(priceText: string): number | null;
/**
 * Parses range values and converts to miles
 */
export declare function parseRange(rangeText: string): number | null;
/**
 * Logs validation results
 */
export declare function logValidationResults(url: string, result: ValidationResult): void;
//# sourceMappingURL=validation.d.ts.map