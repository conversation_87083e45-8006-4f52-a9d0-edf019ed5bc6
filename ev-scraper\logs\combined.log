{
  message: 'Checking robots.txt permission',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:53:45'
}
{
  message: 'Robots.txt allows scraping',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:53:45'
}
{
  service: 'ev-scraper',
  config: {
    delay_between_requests: 2000,
    max_concurrent_requests: 3,
    max_retries: 3,
    retry_delay: 5000,
    headless: true,
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport_width: 1920,
    viewport_height: 1080,
    output_directory: 'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output',
    save_json: true,
    save_csv: true,
    save_sql: true,
    max_pages: undefined,
    specific_makes: undefined,
    year_range: undefined
  },
  timestamp: '2025-07-16 17:53:45',
  level: 'info',
  message: 'Starting EV data scraping'
}
{
  service: 'ev-scraper',
  config: {
    delay_between_requests: 2000,
    max_concurrent_requests: 3,
    max_retries: 3,
    retry_delay: 5000,
    headless: true,
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport_width: 1920,
    viewport_height: 1080,
    output_directory: 'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output',
    save_json: true,
    save_csv: true,
    save_sql: true,
    max_pages: undefined,
    specific_makes: undefined,
    year_range: undefined
  },
  timestamp: '2025-07-16 17:53:45',
  level: 'info',
  message: 'Starting EV scraping session'
}
{
  service: 'ev-scraper',
  headless: true,
  viewport: '1920x1080',
  level: 'info',
  message: 'Initializing browser',
  timestamp: '2025-07-16 17:53:45'
}
{
  service: 'ev-scraper',
  outputDir: 'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output',
  level: 'info',
  message: 'Output directory ready',
  timestamp: '2025-07-16 17:53:45'
}
{
  message: 'Browser initialized successfully',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:53:46'
}
{
  message: 'Discovering car URLs',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:53:46'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car',
  status: 404,
  level: 'warn',
  message: 'HTTP error response',
  timestamp: '2025-07-16 17:53:48'
}
{
  service: 'ev-scraper',
  error: 'Failed to load car listing page',
  level: 'error',
  message: 'Failed to discover car URLs',
  timestamp: '2025-07-16 17:53:48'
}
{
  service: 'ev-scraper',
  error: 'Failed to load car listing page',
  result: {
    success: false,
    total_vehicles: 0,
    successful_scrapes: 0,
    failed_scrapes: 0,
    errors: [],
    execution_time_ms: 3283,
    output_files: []
  },
  level: 'error',
  message: 'Scraping session failed',
  timestamp: '2025-07-16 17:53:48'
}
{
  message: 'Browser closed successfully',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:53:48'
}
{
  message: 'Checking robots.txt permission',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:55:23'
}
{
  message: 'Robots.txt allows scraping',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:55:23'
}
{
  service: 'ev-scraper',
  config: {
    delay_between_requests: 2000,
    max_concurrent_requests: 3,
    max_retries: 3,
    retry_delay: 5000,
    headless: true,
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport_width: 1920,
    viewport_height: 1080,
    output_directory: 'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output',
    save_json: true,
    save_csv: true,
    save_sql: true,
    max_pages: undefined,
    specific_makes: undefined,
    year_range: undefined
  },
  timestamp: '2025-07-16 17:55:23',
  level: 'info',
  message: 'Starting EV data scraping'
}
{
  service: 'ev-scraper',
  config: {
    delay_between_requests: 2000,
    max_concurrent_requests: 3,
    max_retries: 3,
    retry_delay: 5000,
    headless: true,
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport_width: 1920,
    viewport_height: 1080,
    output_directory: 'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output',
    save_json: true,
    save_csv: true,
    save_sql: true,
    max_pages: undefined,
    specific_makes: undefined,
    year_range: undefined
  },
  timestamp: '2025-07-16 17:55:23',
  level: 'info',
  message: 'Starting EV scraping session'
}
{
  service: 'ev-scraper',
  headless: true,
  viewport: '1920x1080',
  level: 'info',
  message: 'Initializing browser',
  timestamp: '2025-07-16 17:55:23'
}
{
  service: 'ev-scraper',
  outputDir: 'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output',
  level: 'info',
  message: 'Output directory ready',
  timestamp: '2025-07-16 17:55:23'
}
{
  message: 'Browser initialized successfully',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:55:24'
}
{
  message: 'Discovering car URLs',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:55:24'
}
{
  service: 'ev-scraper',
  count: 10,
  level: 'info',
  message: 'Found car links on main page',
  timestamp: '2025-07-16 17:55:27'
}
{
  service: 'ev-scraper',
  currentPage: 1,
  level: 'info',
  message: 'No more pages found',
  timestamp: '2025-07-16 17:55:27'
}
{
  service: 'ev-scraper',
  totalFound: 10,
  afterFiltering: 10,
  level: 'info',
  message: 'Car URL discovery completed',
  timestamp: '2025-07-16 17:55:27'
}
{
  service: 'ev-scraper',
  totalItems: 10,
  startTime: '2025-07-16T16:55:27.771Z',
  level: 'info',
  message: 'Scraping session started',
  timestamp: '2025-07-16 17:55:27'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:39'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:39'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:40'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:40'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:40'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:40'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:53'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:53'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:53'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:53'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:55:53'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:55:53'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:16'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:16'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at runNextTicks (node:internal/process/task_queues:65:5)\n' +
    '    at processImmediate (node:internal/timers:459:9)\n' +
    '    at process.topLevelDomainCallback (node:domain:161:15)\n' +
    '    at process.callbackTrampoline (node:internal/async_hooks:128:24)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:56:16',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:17'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:17'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:56:17',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:17'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:17'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:56:17',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:20'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:20'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  errors: [
    'Missing required field: make',
    'Missing required field: model',
    'Missing required field: year'
  ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:20'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:20'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:21'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:21'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  errors: [
    'Missing required field: make',
    'Missing required field: model',
    'Missing required field: year'
  ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  errors: [
    'Missing required field: make',
    'Missing required field: model',
    'Missing required field: year'
  ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:42'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:42'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:42'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:42'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:56:43'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:56:43'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  errors: [
    'Missing required field: make',
    'Missing required field: model',
    'Missing required field: year'
  ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:05'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:05'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  error: 'Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:05',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:06'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:06'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:06',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:06'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:06'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:06',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:09'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:09'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:10'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:10'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:10'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:10'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:17'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:17'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:18'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:18'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:19'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:19'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:31'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:32'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:32'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:32'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:32'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:54'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:54'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at runNextTicks (node:internal/process/task_queues:65:5)\n' +
    '    at processImmediate (node:internal/timers:459:9)\n' +
    '    at process.topLevelDomainCallback (node:domain:161:15)\n' +
    '    at process.callbackTrampoline (node:internal/async_hooks:128:24)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:54',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:55'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:55'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:55',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:56'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:56'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:56',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:57:58'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:57:58'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:58:06'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:58:06'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:58:20'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:58:20'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 17:58:44'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  attempt: 4,
  retriesLeft: 0,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 17:58:44'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:58:44',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  filePath: 'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output\\scraping-errors-2025-07-16T16-55-23.json',
  count: 10,
  level: 'info',
  message: 'Errors file saved',
  timestamp: '2025-07-16 17:58:44'
}
{
  service: 'ev-scraper',
  totalItems: 10,
  successful: 0,
  failed: 10,
  successRate: '0.0%',
  totalTimeMs: 196311,
  avgTimePerItemMs: 19631.1,
  startTime: '2025-07-16T16:55:27.771Z',
  endTime: '2025-07-16T16:58:44.082Z',
  level: 'info',
  message: 'Scraping session completed',
  timestamp: '2025-07-16 17:58:44'
}
{
  service: 'ev-scraper',
  success: true,
  total_vehicles: 10,
  successful_scrapes: 0,
  failed_scrapes: 10,
  errors: [
    {
      url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:56:16.718Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:56:17.351Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:56:17.512Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
      error_message: 'Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year',
      timestamp: '2025-07-16T16:57:05.975Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:57:06.608Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:57:06.911Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:57:54.909Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:57:55.863Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:57:56.495Z',
      retry_count: 3
    },
    {
      url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
      error_message: 'Data validation failed: Missing required field: year',
      timestamp: '2025-07-16T16:58:44.079Z',
      retry_count: 3
    }
  ],
  execution_time_ms: 200704,
  output_files: [
    'C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\output\\scraping-errors-2025-07-16T16-55-23.json'
  ],
  level: 'info',
  message: 'Scraping session completed successfully',
  timestamp: '2025-07-16 17:58:44'
}
{
  message: 'Browser closed successfully',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 17:58:44'
}
{
  service: 'ev-scraper',
  config: {
    delay_between_requests: 3000,
    max_concurrent_requests: 2,
    max_retries: 3,
    retry_delay: 5000,
    headless: true,
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport_width: 1920,
    viewport_height: 1080,
    output_directory: './examples/output',
    save_json: true,
    save_csv: true,
    save_sql: false,
    max_pages: 3,
    specific_makes: [ 'Tesla' ],
    year_range: undefined
  },
  timestamp: '2025-07-16 18:00:08',
  level: 'info',
  message: 'Starting EV scraping session'
}
{
  service: 'ev-scraper',
  headless: true,
  viewport: '1920x1080',
  level: 'info',
  message: 'Initializing browser',
  timestamp: '2025-07-16 18:00:08'
}
{
  service: 'ev-scraper',
  outputDir: './examples/output',
  level: 'info',
  message: 'Output directory ready',
  timestamp: '2025-07-16 18:00:08'
}
{
  message: 'Browser initialized successfully',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 18:00:10'
}
{
  message: 'Discovering car URLs',
  level: 'info',
  service: 'ev-scraper',
  timestamp: '2025-07-16 18:00:10'
}
{
  service: 'ev-scraper',
  count: 10,
  level: 'info',
  message: 'Found car links on main page',
  timestamp: '2025-07-16 18:00:15'
}
{
  service: 'ev-scraper',
  currentPage: 1,
  level: 'info',
  message: 'No more pages found',
  timestamp: '2025-07-16 18:00:15'
}
{
  service: 'ev-scraper',
  totalFound: 10,
  afterFiltering: 3,
  level: 'info',
  message: 'Car URL discovery completed',
  timestamp: '2025-07-16 18:00:15'
}
{
  service: 'ev-scraper',
  totalItems: 3,
  startTime: '2025-07-16T17:00:15.209Z',
  level: 'info',
  message: 'Scraping session started',
  timestamp: '2025-07-16 18:00:15'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 18:00:19'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 18:00:19'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 18:00:19'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  attempt: 1,
  retriesLeft: 3,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 18:00:19'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 18:00:28'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 18:00:28'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 18:00:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  attempt: 2,
  retriesLeft: 2,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 18:00:29'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 18:00:43'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 18:00:43'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  errors: [ 'Missing required field: year' ],
  warnings: [],
  level: 'warn',
  message: 'Data validation failed',
  timestamp: '2025-07-16 18:00:44'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  attempt: 3,
  retriesLeft: 1,
  error: 'Data validation failed: Missing required field: year',
  level: 'warn',
  message: 'Retry attempt failed',
  timestamp: '2025-07-16 18:00:44'
}
