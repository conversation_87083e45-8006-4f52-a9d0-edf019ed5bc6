'use client'

import { useState, useEffect, useCallback } from 'react'
import type { EVModel, EVManufacturer } from '@/shared/types'

interface HomepageStats {
  totalModels: number
  totalManufacturers: number
  averageRange: number
  averagePrice: number
}

interface PopularComparison {
  id: string
  title: string
  models: {
    id: string
    make: string
    model: string
    year: number
    price_msrp: number | null
    range_epa_miles: number | null
    images: string[] | null
  }[]
  comparisonCount: number
}

interface UseHomepageDataReturn {
  featuredModels: EVModel[]
  popularModels: EVModel[]
  bestValueModels: EVModel[]
  editorChoiceModels: EVModel[]
  stats: HomepageStats | null
  popularComparisons: PopularComparison[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useHomepageData(): UseHomepageDataReturn {
  const [featuredModels, setFeaturedModels] = useState<EVModel[]>([])
  const [popularModels, setPopularModels] = useState<EVModel[]>([])
  const [bestValueModels, setBestValueModels] = useState<EVModel[]>([])
  const [editorChoiceModels, setEditorChoiceModels] = useState<EVModel[]>([])
  const [stats, setStats] = useState<HomepageStats | null>(null)
  const [popularComparisons, setPopularComparisons] = useState<PopularComparison[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchHomepageData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      // Fetch featured models
      const featuredResponse = await fetch('/api/ev-models?featured=true&limit=6&sort_by=popularity_score&sort_order=desc')
      if (!featuredResponse.ok) throw new Error('Failed to fetch featured models')
      const featuredData = await featuredResponse.json()

      // Fetch popular models (highest popularity score)
      const popularResponse = await fetch('/api/ev-models?limit=6&sort_by=popularity_score&sort_order=desc&production_status=current')
      if (!popularResponse.ok) throw new Error('Failed to fetch popular models')
      const popularData = await popularResponse.json()

      // Fetch best value models
      const bestValueResponse = await fetch('/api/ev-models?bestValue=true&limit=6&sort_by=price_msrp&sort_order=asc')
      if (!bestValueResponse.ok) throw new Error('Failed to fetch best value models')
      const bestValueData = await bestValueResponse.json()

      // Fetch editor's choice models
      const editorChoiceResponse = await fetch('/api/ev-models?limit=6&sort_by=popularity_score&sort_order=desc')
      if (!editorChoiceResponse.ok) throw new Error('Failed to fetch editor choice models')
      const editorChoiceData = await editorChoiceResponse.json()

      // Fetch stats (we'll calculate from the popular models response for now)
      const statsData = popularData.data || []
      const calculatedStats: HomepageStats = {
        totalModels: popularData.pagination?.total || 0,
        totalManufacturers: new Set(statsData.map((model: EVModel) => model.make)).size,
        averageRange: Math.round(
          statsData
            .filter((model: EVModel) => model.range_epa_miles)
            .reduce((sum: number, model: EVModel) => sum + (model.range_epa_miles || 0), 0) /
          statsData.filter((model: EVModel) => model.range_epa_miles).length
        ) || 0,
        averagePrice: Math.round(
          statsData
            .filter((model: EVModel) => model.price_msrp)
            .reduce((sum: number, model: EVModel) => sum + (model.price_msrp || 0), 0) /
          statsData.filter((model: EVModel) => model.price_msrp).length / 100
        ) || 0 // Convert to dollars
      }

      // Mock popular comparisons for now (in real app, this would come from comparison analytics)
      const mockComparisons: PopularComparison[] = [
        {
          id: '1',
          title: 'Tesla Model 3 vs BMW i4',
          models: featuredData.data?.slice(0, 2) || [],
          comparisonCount: 1247
        },
        {
          id: '2', 
          title: 'Family SUV Showdown',
          models: featuredData.data?.slice(2, 4) || [],
          comparisonCount: 892
        },
        {
          id: '3',
          title: 'Budget EV Comparison',
          models: bestValueData.data?.slice(0, 2) || [],
          comparisonCount: 634
        }
      ]

      setFeaturedModels(featuredData.data || [])
      setPopularModels(popularData.data || [])
      setBestValueModels(bestValueData.data || [])
      setEditorChoiceModels(editorChoiceData.data?.filter((model: EVModel) => model.editor_choice) || [])
      setStats(calculatedStats)
      setPopularComparisons(mockComparisons)

    } catch (err) {
      console.error('Error fetching homepage data:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch homepage data')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchHomepageData()
  }, [fetchHomepageData])

  return {
    featuredModels,
    popularModels,
    bestValueModels,
    editorChoiceModels,
    stats,
    popularComparisons,
    loading,
    error,
    refetch: fetchHomepageData
  }
}
