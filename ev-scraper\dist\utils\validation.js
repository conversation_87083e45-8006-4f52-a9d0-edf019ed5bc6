"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateEVModel = validateEVModel;
exports.extractNumericValue = extractNumericValue;
exports.cleanText = cleanText;
exports.parsePrice = parsePrice;
exports.parseRange = parseRange;
exports.logValidationResults = logValidationResults;
const config_1 = require("./config");
const logger_1 = require("./logger");
/**
 * Validates and cleans scraped EV data
 */
function validateEVModel(data) {
    const errors = [];
    const warnings = [];
    const cleanedData = { ...data };
    // Check required fields
    for (const field of config_1.validationRules.required_fields) {
        if (!data[field]) {
            errors.push(`Missing required field: ${field}`);
        }
    }
    // Validate and clean year
    if (data.year) {
        const year = parseInt(String(data.year));
        if (isNaN(year) || year < config_1.validationRules.ranges.year.min || year > config_1.validationRules.ranges.year.max) {
            errors.push(`Invalid year: ${data.year}. Must be between ${config_1.validationRules.ranges.year.min} and ${config_1.validationRules.ranges.year.max}`);
        }
        else {
            cleanedData.year = year;
        }
    }
    // Validate and clean price (convert to cents)
    if (data.price_msrp) {
        const price = parseFloat(String(data.price_msrp));
        if (isNaN(price) || price < config_1.validationRules.ranges.price.min || price > config_1.validationRules.ranges.price.max) {
            warnings.push(`Questionable price: $${price}`);
        }
        else {
            // Convert to cents if it's in dollars
            cleanedData.price_msrp = price < 10000 ? Math.round(price * 100) : Math.round(price);
        }
    }
    // Validate battery capacity
    if (data.battery_capacity_kwh) {
        const battery = parseFloat(String(data.battery_capacity_kwh));
        if (isNaN(battery) || battery < config_1.validationRules.ranges.battery_capacity.min || battery > config_1.validationRules.ranges.battery_capacity.max) {
            errors.push(`Invalid battery capacity: ${data.battery_capacity_kwh} kWh`);
        }
        else {
            cleanedData.battery_capacity_kwh = battery;
        }
    }
    // Validate range
    if (data.range_epa_miles) {
        const range = parseFloat(String(data.range_epa_miles));
        if (isNaN(range) || range < config_1.validationRules.ranges.range_miles.min || range > config_1.validationRules.ranges.range_miles.max) {
            warnings.push(`Questionable range: ${data.range_epa_miles} miles`);
        }
        else {
            cleanedData.range_epa_miles = range;
        }
    }
    // Validate charging speed
    if (data.charging_speed_dc_kw) {
        const charging = parseFloat(String(data.charging_speed_dc_kw));
        if (isNaN(charging) || charging < config_1.validationRules.ranges.charging_speed.min || charging > config_1.validationRules.ranges.charging_speed.max) {
            warnings.push(`Questionable charging speed: ${data.charging_speed_dc_kw} kW`);
        }
        else {
            cleanedData.charging_speed_dc_kw = charging;
        }
    }
    // Validate acceleration
    if (data.acceleration_0_60_mph) {
        const accel = parseFloat(String(data.acceleration_0_60_mph));
        if (isNaN(accel) || accel < config_1.validationRules.ranges.acceleration.min || accel > config_1.validationRules.ranges.acceleration.max) {
            warnings.push(`Questionable acceleration: ${data.acceleration_0_60_mph} seconds`);
        }
        else {
            cleanedData.acceleration_0_60_mph = accel;
        }
    }
    // Validate top speed
    if (data.top_speed_mph) {
        const speed = parseFloat(String(data.top_speed_mph));
        if (isNaN(speed) || speed < config_1.validationRules.ranges.top_speed.min || speed > config_1.validationRules.ranges.top_speed.max) {
            warnings.push(`Questionable top speed: ${data.top_speed_mph} mph`);
        }
        else {
            cleanedData.top_speed_mph = speed;
        }
    }
    // Validate efficiency
    if (data.efficiency_mpge) {
        const efficiency = parseFloat(String(data.efficiency_mpge));
        if (isNaN(efficiency) || efficiency < config_1.validationRules.ranges.efficiency.min || efficiency > config_1.validationRules.ranges.efficiency.max) {
            warnings.push(`Questionable efficiency: ${data.efficiency_mpge} MPGe`);
        }
        else {
            cleanedData.efficiency_mpge = efficiency;
        }
    }
    // Validate body type
    if (data.body_type) {
        const bodyType = String(data.body_type).toLowerCase().trim();
        if (!config_1.validationRules.valid_body_types.includes(bodyType)) {
            warnings.push(`Unknown body type: ${data.body_type}`);
        }
        else {
            cleanedData.body_type = bodyType;
        }
    }
    // Validate drivetrain
    if (data.drivetrain) {
        const drivetrain = String(data.drivetrain).toUpperCase().trim();
        if (!config_1.validationRules.valid_drivetrains.includes(drivetrain)) {
            warnings.push(`Unknown drivetrain: ${data.drivetrain}`);
        }
        else {
            cleanedData.drivetrain = drivetrain;
        }
    }
    // Validate production status
    if (data.production_status) {
        const status = String(data.production_status).toLowerCase().trim();
        if (!config_1.validationRules.valid_production_status.includes(status)) {
            warnings.push(`Unknown production status: ${data.production_status}`);
        }
        else {
            cleanedData.production_status = status;
        }
    }
    // Clean string fields
    if (data.make) {
        cleanedData.make = String(data.make).trim();
    }
    if (data.model) {
        cleanedData.model = String(data.model).trim();
    }
    if (data.trim) {
        cleanedData.trim = String(data.trim).trim();
    }
    // Validate seating capacity
    if (data.seating_capacity) {
        const seating = parseInt(String(data.seating_capacity));
        if (isNaN(seating) || seating < 1 || seating > 12) {
            warnings.push(`Questionable seating capacity: ${data.seating_capacity}`);
        }
        else {
            cleanedData.seating_capacity = seating;
        }
    }
    // Add metadata
    cleanedData.scraped_at = new Date().toISOString();
    cleanedData.last_updated = new Date().toISOString();
    const isValid = errors.length === 0;
    return {
        isValid,
        errors,
        warnings,
        cleanedData: isValid ? cleanedData : undefined
    };
}
/**
 * Extracts numeric value from text using regex patterns
 */
function extractNumericValue(text, pattern) {
    if (!text)
        return null;
    const match = text.match(pattern);
    if (match && match[1]) {
        const value = parseFloat(match[1].replace(/,/g, ''));
        return isNaN(value) ? null : value;
    }
    return null;
}
/**
 * Cleans and normalizes text data
 */
function cleanText(text) {
    if (!text)
        return '';
    return text
        .trim()
        .replace(/\s+/g, ' ') // Replace multiple spaces with single space
        .replace(/[^\w\s.-]/g, ''); // Remove special characters except dots and hyphens
}
/**
 * Converts various price formats to cents
 */
function parsePrice(priceText) {
    if (!priceText)
        return null;
    // Remove currency symbols and commas
    const cleaned = priceText.replace(/[$,€£¥]/g, '').trim();
    const value = parseFloat(cleaned);
    if (isNaN(value))
        return null;
    // If value is less than 10,000, assume it's in thousands (e.g., "45.5" = $45,500)
    if (value < 10000) {
        return Math.round(value * 100000); // Convert to cents
    }
    // Otherwise assume it's already in dollars
    return Math.round(value * 100); // Convert to cents
}
/**
 * Parses range values and converts to miles
 */
function parseRange(rangeText) {
    if (!rangeText)
        return null;
    const kmMatch = rangeText.match(/(\d+(?:\.\d+)?)\s*km/i);
    const miMatch = rangeText.match(/(\d+(?:\.\d+)?)\s*(miles?|mi)/i);
    if (miMatch) {
        return parseFloat(miMatch[1]);
    }
    else if (kmMatch) {
        // Convert km to miles (1 km = 0.621371 miles)
        return Math.round(parseFloat(kmMatch[1]) * 0.621371);
    }
    // Try to extract just the number
    const numberMatch = rangeText.match(/(\d+(?:\.\d+)?)/);
    if (numberMatch) {
        const value = parseFloat(numberMatch[1]);
        // If it's a reasonable range value, assume it's in miles
        if (value >= 50 && value <= 800) {
            return value;
        }
        // If it's larger, assume it's in km
        if (value > 800) {
            return Math.round(value * 0.621371);
        }
    }
    return null;
}
/**
 * Logs validation results
 */
function logValidationResults(url, result) {
    if (!result.isValid) {
        logger_1.logger.warn('Data validation failed', {
            url,
            errors: result.errors,
            warnings: result.warnings
        });
    }
    else if (result.warnings.length > 0) {
        logger_1.logger.debug('Data validation warnings', {
            url,
            warnings: result.warnings
        });
    }
}
//# sourceMappingURL=validation.js.map