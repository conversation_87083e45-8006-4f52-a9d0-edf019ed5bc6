export interface EVModel {
  id?: string;
  make: string;
  model: string;
  year: number;
  trim?: string;
  variant?: string;
  price_msrp?: number;
  price_base?: number;
  price_including_incentives?: number;
  currency?: string;
  battery_capacity_kwh: number;
  battery_usable_kwh?: number;
  range_epa_miles?: number;
  range_wltp_km?: number;
  range_real_world_miles?: number;
  charging_speed_ac_kw?: number;
  charging_speed_dc_kw?: number;
  charging_time_10_80_minutes?: number;
  charging_time_0_100_minutes?: number;
  charging_port_type?: string;
  acceleration_0_60_mph?: number;
  acceleration_0_100_kmh?: number;
  top_speed_mph?: number;
  top_speed_kmh?: number;
  efficiency_mpge?: number;
  efficiency_kwh_100km?: number;
  efficiency_miles_kwh?: number;
  body_type?: string;
  seating_capacity?: number;
  doors?: number;
  cargo_space_cu_ft?: number;
  cargo_space_liters?: number;
  length_mm?: number;
  width_mm?: number;
  height_mm?: number;
  wheelbase_mm?: number;
  weight_kg?: number;
  drivetrain?: "FWD" | "RWD" | "AWD" | "4WD";
  motor_power_kw?: number;
  motor_power_hp?: number;
  motor_torque_nm?: number;
  production_status?: "current" | "upcoming" | "discontinued";
  availability_region?: string[];
  manufacturer_id?: string;
  images?: string[];
  source_url?: string;
  scraped_at?: string;
  last_updated?: string;
  is_featured?: boolean;
  best_value?: boolean;
  editor_choice?: boolean;
}

export interface DatabaseStats {
  total_vehicles: number;
  total_scraped_urls: number;
  successful_scrapes: number;
  failed_scrapes: number;
  last_scrape_date?: string;
  makeDistribution?: Record<string, number>;
  bodyTypeDistribution?: Record<string, number>;
  yearDistribution?: Record<number, number>;
  priceRangeDistribution?: Record<string, number>;
  averageRange?: number;
  averagePrice?: number;
}

export interface FilterOptions {
  makes: string[];
  bodyTypes: string[];
  years: number[];
  priceRange: {
    min: number;
    max: number;
  };
  rangeRange: {
    min: number;
    max: number;
  };
}

export interface VehicleFilters {
  make?: string;
  model?: string;
  year?: number;
  minPrice?: number;
  maxPrice?: number;
  minRange?: number;
  bodyType?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
