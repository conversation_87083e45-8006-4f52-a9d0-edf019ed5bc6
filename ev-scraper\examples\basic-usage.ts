/**
 * Basic usage example for the EV scraper
 * This demonstrates how to use the scraper programmatically
 */

import { EVScraper } from '../src/scraper/EVScraper'
import { defaultConfig } from '../src/utils/config'
import { logger } from '../src/utils/logger'

async function basicExample() {
  console.log('🚗 Starting basic EV scraping example...\n')

  // Create custom configuration
  const config = {
    ...defaultConfig,
    // Conservative settings for example
    delay_between_requests: 3000,  // 3 seconds between requests
    max_concurrent_requests: 2,    // Only 2 concurrent requests
    max_pages: 3,                  // Limit to 3 pages for demo
    specific_makes: ['Tesla'],     // Only scrape Tesla models
    
    // Output settings
    output_directory: './examples/output',
    save_json: true,
    save_csv: true,
    save_sql: false,  // Skip SQL for this example
    
    // Browser settings
    headless: true,   // Run in headless mode
  }

  try {
    // Create and run scraper
    const scraper = new EVScraper(config)
    const result = await scraper.scrape()

    // Print results
    console.log('\n📊 Scraping Results:')
    console.log(`✅ Success: ${result.success}`)
    console.log(`📈 Total vehicles: ${result.total_vehicles}`)
    console.log(`✅ Successful scrapes: ${result.successful_scrapes}`)
    console.log(`❌ Failed scrapes: ${result.failed_scrapes}`)
    console.log(`⏱️  Execution time: ${(result.execution_time_ms / 1000).toFixed(1)}s`)

    if (result.output_files.length > 0) {
      console.log('\n📁 Output files:')
      result.output_files.forEach(file => {
        console.log(`   • ${file}`)
      })
    }

    if (result.errors.length > 0) {
      console.log(`\n⚠️  Errors encountered: ${result.errors.length}`)
      result.errors.slice(0, 3).forEach(error => {
        console.log(`   • ${error.url}: ${error.error_message}`)
      })
      if (result.errors.length > 3) {
        console.log(`   • ... and ${result.errors.length - 3} more`)
      }
    }

    console.log('\n✨ Example completed successfully!')

  } catch (error) {
    console.error('❌ Example failed:', error.message)
    logger.error('Example failed', { error: error.message })
  }
}

// Run the example
if (require.main === module) {
  basicExample().catch(console.error)
}

export { basicExample }
