'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  BookOpen, 
  Calculator, 
  MapPin, 
  Star, 
  ArrowRight,
  Lightbulb,
  DollarSign,
  Zap,
  Shield,
  Leaf
} from 'lucide-react'
import Link from 'next/link'

export function EducationalSection() {
  const educationalCards = [
    {
      id: 'new-to-evs',
      title: 'New to EVs?',
      description: 'Everything you need to know about electric vehicles',
      icon: <Lightbulb className="h-6 w-6" />,
      color: 'bg-blue-500',
      badge: 'Beginner Guide',
      content: [
        'How electric cars work',
        'Types of EVs available',
        'Charging basics explained',
        'Cost comparison vs gas cars'
      ],
      cta: 'Learn EV Basics',
      href: '/ev-models?search=beginner+guide'
    },
    {
      id: 'cost-calculator',
      title: 'Cost Calculator',
      description: 'Calculate your potential savings with an EV',
      icon: <Calculator className="h-6 w-6" />,
      color: 'bg-green-500',
      badge: 'Savings Tool',
      content: [
        'Fuel cost savings',
        'Maintenance savings',
        'Tax incentives',
        'Total cost of ownership'
      ],
      cta: 'Calculate Savings',
      href: '/ev-models?search=cost+analysis'
    },
    {
      id: 'charging-guide',
      title: 'Charging 101',
      description: 'Master EV charging at home and on the road',
      icon: <Zap className="h-6 w-6" />,
      color: 'bg-purple-500',
      badge: 'Essential Guide',
      content: [
        'Home charging setup',
        'Public charging networks',
        'Charging speeds explained',
        'Trip planning tips'
      ],
      cta: 'Learn About Charging',
      href: '/charging'
    },
    {
      id: 'real-reviews',
      title: 'Real-World Reviews',
      description: 'Honest insights from actual EV owners',
      icon: <Star className="h-6 w-6" />,
      color: 'bg-amber-500',
      badge: 'Owner Insights',
      content: [
        'Long-term ownership costs',
        'Real-world range tests',
        'Reliability reports',
        'Owner satisfaction ratings'
      ],
      cta: 'Read Reviews',
      href: '/ev-models?search=reviews'
    }
  ]

  const quickFacts = [
    {
      icon: <DollarSign className="h-5 w-5 text-green-600" />,
      title: 'Lower Operating Costs',
      description: 'EVs cost ~60% less to fuel than gas cars'
    },
    {
      icon: <Shield className="h-5 w-5 text-blue-600" />,
      title: 'Fewer Moving Parts',
      description: 'Less maintenance with no oil changes needed'
    },
    {
      icon: <Leaf className="h-5 w-5 text-emerald-600" />,
      title: 'Zero Emissions',
      description: 'No tailpipe emissions for cleaner air'
    },
    {
      icon: <Zap className="h-5 w-5 text-purple-600" />,
      title: 'Instant Torque',
      description: 'Immediate acceleration from electric motors'
    }
  ]

  return (
    <section className="bg-white py-16 dark:bg-gray-900">
      <div className="container">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <Badge variant="secondary" className="mb-4">
            <BookOpen className="mr-1 h-3 w-3" />
            Learn About EVs
          </Badge>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl">
            Your EV Education Hub
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            Get informed with our comprehensive guides, tools, and real-world insights
          </p>
        </div>

        {/* Educational Cards Grid */}
        <div className="mb-12 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {educationalCards.map((card) => (
            <Card key={card.id} className="group overflow-hidden border-0 shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className={`rounded-lg p-3 ${card.color} text-white`}>
                    {card.icon}
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {card.badge}
                  </Badge>
                </div>
                <CardTitle className="text-lg">{card.title}</CardTitle>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {card.description}
                </p>
              </CardHeader>
              
              <CardContent className="pt-0">
                <ul className="mb-4 space-y-2 text-sm">
                  {card.content.map((item, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-electric-600" />
                      {item}
                    </li>
                  ))}
                </ul>
                
                <Link href={card.href}>
                  <Button variant="outline" className="w-full group">
                    {card.cta}
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Facts */}
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Facts Grid */}
          <div>
            <h3 className="mb-6 text-xl font-semibold text-gray-900 dark:text-white">
              Why Choose Electric?
            </h3>
            <div className="grid gap-4 sm:grid-cols-2">
              {quickFacts.map((fact, index) => (
                <div key={index} className="flex items-start gap-3 rounded-lg border p-4">
                  <div className="mt-0.5">{fact.icon}</div>
                  <div>
                    <h4 className="font-semibold text-sm">{fact.title}</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {fact.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Card */}
          <Card className="border-electric-200 bg-electric-50 dark:border-electric-800 dark:bg-electric-950">
            <CardContent className="p-8">
              <div className="mb-4">
                <MapPin className="mx-auto h-12 w-12 text-electric-600" />
              </div>
              <h3 className="mb-2 text-xl font-semibold text-electric-900 dark:text-electric-100">
                Ready to Go Electric?
              </h3>
              <p className="mb-6 text-electric-800 dark:text-electric-200">
                Start your journey with our comprehensive EV database and comparison tools
              </p>
              <div className="space-y-3">
                <Link href="/ev-models">
                  <Button className="w-full bg-electric-600 hover:bg-electric-700 group">
                    Browse All EVs
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
                <Link href="/compare">
                  <Button variant="outline" className="w-full group">
                    Compare Models
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
