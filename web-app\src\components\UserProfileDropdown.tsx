'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  DropdownMenu,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import {
  User,
  Settings,
  LogOut,
  LayoutDashboard,
  Car,
  Zap,
  BarChart3,
  UserCircle,
} from 'lucide-react'

interface UserProfileDropdownProps {
  variant?: 'default' | 'dashboard'
}

export function UserProfileDropdown({ variant = 'default' }: UserProfileDropdownProps) {
  const { user, profile, signOut } = useAuth()
  const pathname = usePathname()

  if (!user) {
    return null
  }

  const handleSignOut = async () => {
    await signOut()
  }

  const isActive = (path: string) => {
    return pathname === path
  }

  // User-specific menu items
  const userMenuItems = [
    {
      href: '/dashboard',
      label: 'Dashboard',
      icon: <LayoutDashboard className="h-4 w-4" />,
      description: 'Overview and quick actions',
    },
    {
      href: '/profile',
      label: 'Profile',
      icon: <User className="h-4 w-4" />,
      description: 'Manage your account',
    },
    {
      href: '/vehicles',
      label: 'My Vehicles',
      icon: <Car className="h-4 w-4" />,
      description: 'Your EV collection',
    },
    {
      href: '/charging',
      label: 'Charging',
      icon: <Zap className="h-4 w-4" />,
      description: 'Charging history & stations',
    },
    {
      href: '/analytics',
      label: 'Analytics',
      icon: <BarChart3 className="h-4 w-4" />,
      description: 'Usage insights & reports',
    },
    {
      href: '/settings',
      label: 'Settings',
      icon: <Settings className="h-4 w-4" />,
      description: 'Account preferences',
    },
  ]

  // Get user display name
  const displayName = profile?.full_name || user?.email?.split('@')[0] || 'User'
  const userEmail = user?.email

  // Create user avatar (first letter of name)
  const avatarLetter = displayName.charAt(0).toUpperCase()

  return (
    <DropdownMenu
      trigger={
        <DropdownMenuTrigger className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="flex items-center gap-2 px-2">
            {/* User Avatar */}
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-electric-600 text-sm font-semibold text-white">
              {avatarLetter}
            </div>

            {/* User Info - Hidden on mobile */}
            <div className="hidden text-left sm:block">
              <div className="text-sm font-medium text-gray-900 dark:text-white">{displayName}</div>
            </div>
          </Button>
        </DropdownMenuTrigger>
      }
      align="right"
    >
      {/* User Info Header */}
      <DropdownMenuLabel>
        <div className="flex items-center gap-3 py-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-electric-600 font-semibold text-white">
            {avatarLetter}
          </div>
          <div className="min-w-0 flex-1">
            <div className="font-semibold text-gray-900 dark:text-white">{displayName}</div>
            {userEmail && (
              <div className="truncate text-xs text-gray-600 dark:text-gray-400">{userEmail}</div>
            )}
          </div>
        </div>
      </DropdownMenuLabel>

      <DropdownMenuSeparator />

      {/* Navigation Items */}
      {userMenuItems.map((item) => (
        <DropdownMenuItem key={item.href} asChild>
          <Link
            href={item.href}
            className={`flex items-center gap-3 px-4 py-2 text-sm transition-colors ${
              isActive(item.href)
                ? 'dark:bg-electric-950 bg-electric-50 text-electric-600 dark:text-electric-400'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100'
            }`}
          >
            <div className="flex flex-1 items-center gap-3">
              {item.icon}
              <div>
                <div className="font-medium">{item.label}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">{item.description}</div>
              </div>
            </div>
          </Link>
        </DropdownMenuItem>
      ))}

      <DropdownMenuSeparator />

      {/* Sign Out */}
      <DropdownMenuItem onClick={handleSignOut}>
        <div className="flex items-center gap-3 text-red-600 dark:text-red-400">
          <LogOut className="h-4 w-4" />
          <div>
            <div className="font-medium">Sign Out</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">End your session</div>
          </div>
        </div>
      </DropdownMenuItem>
    </DropdownMenu>
  )
}
