import express from 'express';
import cors from 'cors';
import path from 'path';
import { EVDatabase } from '../../src/database';

const app = express();
const port = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize database connection
const dbPath = path.join(__dirname, '../../data/ev-scraper.db');
const database = new EVDatabase(dbPath);

// API Routes

// Get all vehicles with filtering and pagination
app.get('/api/vehicles', async (req, res) => {
  try {
    const {
      make,
      model,
      year,
      minPrice,
      maxPrice,
      minRange,
      bodyType,
      limit = '50',
      offset = '0',
      sortBy = 'make',
      sortOrder = 'asc'
    } = req.query;

    const filters = {
      make: make as string,
      model: model as string,
      year: year ? parseInt(year as string) : undefined,
      minPrice: minPrice ? parseInt(minPrice as string) * 100 : undefined, // Convert to cents
      maxPrice: maxPrice ? parseInt(maxPrice as string) * 100 : undefined,
      minRange: minRange ? parseInt(minRange as string) : undefined,
      bodyType: bodyType as string,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
    };

    const vehicles = database.getVehicles(filters);
    
    // Sort vehicles
    vehicles.sort((a, b) => {
      const aVal = a[sortBy as keyof typeof a];
      const bVal = b[sortBy as keyof typeof b];
      
      if (aVal === null || aVal === undefined) return 1;
      if (bVal === null || bVal === undefined) return -1;
      
      if (sortOrder === 'desc') {
        return aVal < bVal ? 1 : -1;
      }
      return aVal > bVal ? 1 : -1;
    });

    res.json({
      vehicles,
      total: vehicles.length,
      filters: filters
    });
  } catch (error) {
    console.error('Error fetching vehicles:', error);
    res.status(500).json({ error: 'Failed to fetch vehicles' });
  }
});

// Get vehicle by ID
app.get('/api/vehicles/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const vehicles = database.getVehicles();
    const vehicle = vehicles.find(v => v.id === id);
    
    if (!vehicle) {
      return res.status(404).json({ error: 'Vehicle not found' });
    }
    
    res.json(vehicle);
  } catch (error) {
    console.error('Error fetching vehicle:', error);
    res.status(500).json({ error: 'Failed to fetch vehicle' });
  }
});

// Get database statistics
app.get('/api/stats', async (req, res) => {
  try {
    const stats = database.getStats();
    
    // Get additional statistics
    const vehicles = database.getVehicles();
    
    const makeStats = vehicles.reduce((acc, vehicle) => {
      acc[vehicle.make] = (acc[vehicle.make] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const bodyTypeStats = vehicles.reduce((acc, vehicle) => {
      const bodyType = vehicle.body_type || 'Unknown';
      acc[bodyType] = (acc[bodyType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const yearStats = vehicles.reduce((acc, vehicle) => {
      acc[vehicle.year] = (acc[vehicle.year] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const priceRanges = vehicles
      .filter(v => v.price_msrp)
      .map(v => Math.floor((v.price_msrp! / 100) / 10000) * 10000) // Group by $10k ranges
      .reduce((acc, range) => {
        const rangeKey = `$${range}k-${range + 10}k`;
        acc[rangeKey] = (acc[rangeKey] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    res.json({
      ...stats,
      makeDistribution: makeStats,
      bodyTypeDistribution: bodyTypeStats,
      yearDistribution: yearStats,
      priceRangeDistribution: priceRanges,
      averageRange: vehicles
        .filter(v => v.range_epa_miles)
        .reduce((sum, v) => sum + (v.range_epa_miles || 0), 0) / 
        vehicles.filter(v => v.range_epa_miles).length || 0,
      averagePrice: vehicles
        .filter(v => v.price_msrp)
        .reduce((sum, v) => sum + (v.price_msrp || 0), 0) / 
        vehicles.filter(v => v.price_msrp).length / 100 || 0, // Convert to dollars
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({ error: 'Failed to fetch statistics' });
  }
});

// Get unique filter values
app.get('/api/filter-options', async (req, res) => {
  try {
    const vehicles = database.getVehicles();
    
    const makes = [...new Set(vehicles.map(v => v.make))].sort();
    const bodyTypes = [...new Set(vehicles.map(v => v.body_type).filter(Boolean))].sort();
    const years = [...new Set(vehicles.map(v => v.year))].sort((a, b) => b - a);
    
    res.json({
      makes,
      bodyTypes,
      years,
      priceRange: {
        min: Math.min(...vehicles.filter(v => v.price_msrp).map(v => v.price_msrp! / 100)),
        max: Math.max(...vehicles.filter(v => v.price_msrp).map(v => v.price_msrp! / 100))
      },
      rangeRange: {
        min: Math.min(...vehicles.filter(v => v.range_epa_miles).map(v => v.range_epa_miles!)),
        max: Math.max(...vehicles.filter(v => v.range_epa_miles).map(v => v.range_epa_miles!))
      }
    });
  } catch (error) {
    console.error('Error fetching filter options:', error);
    res.status(500).json({ error: 'Failed to fetch filter options' });
  }
});

// Export data
app.get('/api/export/:format', async (req, res) => {
  try {
    const { format } = req.params;
    const vehicles = database.getVehicles();
    
    if (format === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=ev-data.json');
      res.json(vehicles);
    } else if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=ev-data.csv');
      
      // Simple CSV conversion
      const headers = Object.keys(vehicles[0] || {});
      const csvContent = [
        headers.join(','),
        ...vehicles.map(vehicle => 
          headers.map(header => {
            const value = vehicle[header as keyof typeof vehicle];
            return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
          }).join(',')
        )
      ].join('\n');
      
      res.send(csvContent);
    } else {
      res.status(400).json({ error: 'Unsupported format' });
    }
  } catch (error) {
    console.error('Error exporting data:', error);
    res.status(500).json({ error: 'Failed to export data' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Start server
app.listen(port, () => {
  console.log(`EV Scraper API server running on http://localhost:${port}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down API server...');
  database.close();
  process.exit(0);
});

export default app;
