'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'
import { ChevronDown } from 'lucide-react'

interface DropdownMenuProps {
  children: React.ReactNode
  trigger: React.ReactNode
  align?: 'left' | 'right'
  className?: string
}

interface DropdownMenuItemProps {
  children: React.ReactNode
  onClick?: () => void
  href?: string
  className?: string
  disabled?: boolean
  asChild?: boolean
}

interface DropdownMenuSeparatorProps {
  className?: string
}

interface DropdownMenuLabelProps {
  children: React.ReactNode
  className?: string
}

const DropdownMenuContext = React.createContext<{
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}>({
  isOpen: false,
  setIsOpen: () => {},
})

export function DropdownMenu({ children, trigger, align = 'right', className }: DropdownMenuProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const dropdownRef = React.useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  // Close dropdown on escape key and handle arrow navigation
  React.useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
      // Add basic arrow key navigation
      if (isOpen && (event.key === 'ArrowDown' || event.key === 'ArrowUp')) {
        event.preventDefault()
        const menuItems = dropdownRef.current?.querySelectorAll('[role="menuitem"]')
        if (menuItems && menuItems.length > 0) {
          const currentIndex = Array.from(menuItems).findIndex(
            (item) => item === document.activeElement
          )
          let nextIndex = currentIndex

          if (event.key === 'ArrowDown') {
            nextIndex = currentIndex < menuItems.length - 1 ? currentIndex + 1 : 0
          } else {
            nextIndex = currentIndex > 0 ? currentIndex - 1 : menuItems.length - 1
          }

          ;(menuItems[nextIndex] as HTMLElement).focus()
        }
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen])

  return (
    <DropdownMenuContext.Provider value={{ isOpen, setIsOpen }}>
      <div className={cn('relative', className)} ref={dropdownRef}>
        <div
          onClick={() => setIsOpen(!isOpen)}
          className="cursor-pointer"
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault()
              setIsOpen(!isOpen)
            }
          }}
          aria-expanded={isOpen}
          aria-haspopup="true"
        >
          {trigger}
        </div>

        {isOpen && (
          <div
            className={cn(
              'absolute top-full z-50 mt-2 min-w-[12rem] overflow-hidden rounded-md border bg-white shadow-lg dark:border-gray-800 dark:bg-gray-900',
              align === 'right' ? 'right-0' : 'left-0'
            )}
            role="menu"
            aria-orientation="vertical"
          >
            <div className="py-1">{children}</div>
          </div>
        )}
      </div>
    </DropdownMenuContext.Provider>
  )
}

export function DropdownMenuItem({
  children,
  onClick,
  href,
  className,
  disabled = false,
  asChild = false,
}: DropdownMenuItemProps) {
  const { setIsOpen } = React.useContext(DropdownMenuContext)

  const handleClick = () => {
    if (!disabled) {
      onClick?.()
      setIsOpen(false)
    }
  }

  const baseClassName = cn(
    'block w-full px-4 py-2 text-left text-sm transition-colors',
    disabled
      ? 'cursor-not-allowed text-gray-400 dark:text-gray-600'
      : 'cursor-pointer text-gray-700 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100',
    className
  )

  if (href && !disabled) {
    return (
      <a href={href} className={baseClassName} onClick={handleClick} role="menuitem">
        {children}
      </a>
    )
  }

  if (asChild) {
    return React.cloneElement(children as React.ReactElement, {
      className: cn((children as React.ReactElement).props.className, baseClassName),
      onClick: handleClick,
      role: 'menuitem',
    })
  }

  return (
    <button className={baseClassName} onClick={handleClick} disabled={disabled} role="menuitem">
      {children}
    </button>
  )
}

export function DropdownMenuSeparator({ className }: DropdownMenuSeparatorProps) {
  return (
    <div className={cn('my-1 h-px bg-gray-200 dark:bg-gray-700', className)} role="separator" />
  )
}

export function DropdownMenuLabel({ children, className }: DropdownMenuLabelProps) {
  return (
    <div
      className={cn(
        'px-4 py-2 text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400',
        className
      )}
      role="presentation"
    >
      {children}
    </div>
  )
}

// Trigger component with chevron icon
export function DropdownMenuTrigger({
  children,
  className,
  showChevron = true,
}: {
  children: React.ReactNode
  className?: string
  showChevron?: boolean
}) {
  const { isOpen } = React.useContext(DropdownMenuContext)

  return (
    <div className={cn('flex items-center gap-1', className)}>
      {children}
      {showChevron && (
        <ChevronDown
          className={cn(
            'h-4 w-4 text-gray-600 transition-transform duration-200 dark:text-gray-400',
            isOpen && 'rotate-180'
          )}
        />
      )}
    </div>
  )
}
