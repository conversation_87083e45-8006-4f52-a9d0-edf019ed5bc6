'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Star, ArrowRight, Zap, MapPin } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import type { EVModel } from '@/shared/types'
import { cn } from '@/lib/utils'

interface FeaturedModelsSectionProps {
  models: EVModel[]
  loading: boolean
  error: string | null
}

export function FeaturedModelsSection({ models, loading, error }: FeaturedModelsSectionProps) {
  if (loading) {
    return (
      <section className="bg-gray-50 py-16 dark:bg-gray-900">
        <div className="container">
          <div className="text-center">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Loading featured EVs...</p>
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="bg-gray-50 py-16 dark:bg-gray-900">
        <div className="container">
          <Alert variant="destructive">
            <AlertDescription>Failed to load featured models. Please try again later.</AlertDescription>
          </Alert>
        </div>
      </section>
    )
  }

  if (!models || models.length === 0) {
    return null
  }

  const formatPrice = (price: number | null) => {
    if (!price) return 'Price TBA'
    return `$${(price / 100).toLocaleString()}`
  }

  const getMainImage = (images: string[] | null) => {
    if (!images || images.length === 0) {
      return 'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=800&h=600&fit=crop&crop=center'
    }
    return images[0]
  }

  return (
    <section className="bg-gray-50 py-16 dark:bg-gray-900">
      <div className="container">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <Badge variant="secondary" className="mb-4">
            <Star className="mr-1 h-3 w-3" />
            Featured EVs
          </Badge>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl">
            Discover Top Electric Vehicles
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            Handpicked electric vehicles that represent the best in innovation, performance, and value
          </p>
        </div>

        {/* Featured Models Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {models.slice(0, 6).map((model) => (
            <Card key={model.id} className="group overflow-hidden border-0 shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
              <div className="relative aspect-[4/3] overflow-hidden">
                <Image
                  src={getMainImage(model.images)}
                  alt={`${model.make} ${model.model}`}
                  fill
                  className="object-cover transition-transform group-hover:scale-105"
                />
                
                {/* Badges */}
                <div className="absolute left-3 top-3 flex flex-col gap-1">
                  {model.is_featured && (
                    <Badge className="bg-electric-600 text-white">
                      <Star className="mr-1 h-3 w-3" />
                      Featured
                    </Badge>
                  )}
                  {model.best_value && <Badge variant="secondary">Best Value</Badge>}
                  {model.editor_choice && (
                    <Badge className="bg-amber-600 text-white">Editor's Choice</Badge>
                  )}
                </div>

                {/* Price Badge */}
                <div className="absolute bottom-3 right-3">
                  <Badge variant="outline" className="bg-white/90 text-gray-900">
                    {formatPrice(model.price_msrp)}
                  </Badge>
                </div>
              </div>

              <CardContent className="p-6">
                <div className="mb-3">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {model.make} {model.model}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {model.year} {model.trim && `• ${model.trim}`}
                  </p>
                </div>

                {/* Key Specs */}
                <div className="mb-4 grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-electric-600" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {model.range_epa_miles ? `${model.range_epa_miles} mi` : 'Range TBA'}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-electric-600" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {model.charging_speed_dc_kw ? `${model.charging_speed_dc_kw}kW` : 'DC Fast'}
                    </span>
                  </div>
                </div>

                {/* CTA */}
                <Link href={`/ev-models/${model.id}`}>
                  <Button className="w-full group">
                    View Details
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All CTA */}
        <div className="mt-12 text-center">
          <Link href="/ev-models">
            <Button size="lg" variant="outline" className="group">
              Browse All {models.length > 6 ? `${models.length}+` : ''} EV Models
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
