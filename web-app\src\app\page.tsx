'use client'

import { <PERSON><PERSON> } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { SearchHeroSection } from '@/components/homepage/SearchHeroSection'
import { FeaturedModelsSection } from '@/components/homepage/FeaturedModelsSection'
import { QuickFiltersSection } from '@/components/homepage/QuickFiltersSection'
import { PopularComparisonsSection } from '@/components/homepage/PopularComparisonsSection'
import { MarketInsightsSection } from '@/components/homepage/MarketInsightsSection'
import { EVFinderSection } from '@/components/homepage/EVFinderSection'
import { EducationalSection } from '@/components/homepage/EducationalSection'
import { useHomepageData } from '@/hooks/useHomepageData'

export default function HomePage() {
  const {
    featuredModels,
    popularModels,
    bestValueModels,
    editorChoiceModels,
    stats,
    popularComparisons,
    loading,
    error,
  } = useHomepageData()

  return (
    <div className="flex min-h-screen flex-col">
      <Header />

      {/* Hero Section with Intelligent Search */}
      <SearchHeroSection totalModels={stats?.totalModels} />

      {/* Featured EV Models Section */}
      <FeaturedModelsSection models={featuredModels} loading={loading} error={error} />

      {/* Quick Decision Filters */}
      <QuickFiltersSection />

      {/* Popular Comparisons */}
      <PopularComparisonsSection comparisons={popularComparisons} loading={loading} />

      {/* Interactive EV Finder */}
      <EVFinderSection />

      {/* Market Insights */}
      <MarketInsightsSection stats={stats} loading={loading} />

      {/* Educational Content */}
      <EducationalSection />

      <Footer />
    </div>
  )
}
