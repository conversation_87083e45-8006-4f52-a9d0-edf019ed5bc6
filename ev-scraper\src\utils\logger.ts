import winston from 'winston'
import path from 'path'
import fs from 'fs-extra'

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs')
fs.ensureDirSync(logsDir)

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
)

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`
    if (Object.keys(meta).length > 0) {
      msg += `\n${JSON.stringify(meta, null, 2)}`
    }
    return msg
  })
)

// Create logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'ev-scraper' },
  transports: [
    // Write all logs to combined.log
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // Write error logs to error.log
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // Write scraping logs to scraping.log
    new winston.transports.File({
      filename: path.join(logsDir, 'scraping.log'),
      level: 'debug',
      maxsize: 10485760, // 10MB
      maxFiles: 3,
    })
  ]
})

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }))
}

// Progress logging utilities
export class ProgressLogger {
  private startTime: Date
  private totalItems: number
  private processedItems: number = 0
  private successfulItems: number = 0
  private failedItems: number = 0

  constructor(totalItems: number) {
    this.startTime = new Date()
    this.totalItems = totalItems
    this.logStart()
  }

  private logStart() {
    logger.info('Scraping session started', {
      totalItems: this.totalItems,
      startTime: this.startTime.toISOString()
    })
  }

  logProgress(url: string, success: boolean, details?: any) {
    this.processedItems++
    if (success) {
      this.successfulItems++
    } else {
      this.failedItems++
    }

    const progress = (this.processedItems / this.totalItems * 100).toFixed(1)
    const elapsed = Date.now() - this.startTime.getTime()
    const avgTimePerItem = elapsed / this.processedItems
    const estimatedTimeRemaining = (this.totalItems - this.processedItems) * avgTimePerItem

    logger.info('Progress update', {
      url,
      success,
      progress: `${progress}%`,
      processed: this.processedItems,
      total: this.totalItems,
      successful: this.successfulItems,
      failed: this.failedItems,
      elapsedMs: elapsed,
      estimatedRemainingMs: estimatedTimeRemaining,
      ...details
    })

    // Log every 10% or every 50 items, whichever is more frequent
    const logInterval = Math.min(Math.ceil(this.totalItems / 10), 50)
    if (this.processedItems % logInterval === 0) {
      this.logSummary()
    }
  }

  logError(url: string, error: Error, retryCount: number = 0) {
    this.failedItems++
    this.processedItems++

    logger.error('Scraping error', {
      url,
      error: error.message,
      stack: error.stack,
      retryCount,
      timestamp: new Date().toISOString()
    })
  }

  logSummary() {
    const elapsed = Date.now() - this.startTime.getTime()
    const progress = (this.processedItems / this.totalItems * 100).toFixed(1)
    const successRate = (this.successfulItems / this.processedItems * 100).toFixed(1)

    logger.info('Scraping summary', {
      progress: `${progress}%`,
      processed: this.processedItems,
      total: this.totalItems,
      successful: this.successfulItems,
      failed: this.failedItems,
      successRate: `${successRate}%`,
      elapsedMs: elapsed,
      avgTimePerItemMs: elapsed / this.processedItems
    })
  }

  logCompletion() {
    const elapsed = Date.now() - this.startTime.getTime()
    const successRate = (this.successfulItems / this.totalItems * 100).toFixed(1)

    logger.info('Scraping session completed', {
      totalItems: this.totalItems,
      successful: this.successfulItems,
      failed: this.failedItems,
      successRate: `${successRate}%`,
      totalTimeMs: elapsed,
      avgTimePerItemMs: elapsed / this.totalItems,
      startTime: this.startTime.toISOString(),
      endTime: new Date().toISOString()
    })
  }
}

// Utility functions for structured logging
export const logScrapingStart = (config: any) => {
  logger.info('Starting EV data scraping', {
    config,
    timestamp: new Date().toISOString()
  })
}

export const logScrapingEnd = (results: any) => {
  logger.info('EV data scraping completed', {
    results,
    timestamp: new Date().toISOString()
  })
}

export const logRateLimitDelay = (delayMs: number) => {
  logger.debug('Rate limit delay', {
    delayMs,
    timestamp: new Date().toISOString()
  })
}

export const logRetryAttempt = (url: string, attempt: number, maxRetries: number) => {
  logger.warn('Retry attempt', {
    url,
    attempt,
    maxRetries,
    timestamp: new Date().toISOString()
  })
}
