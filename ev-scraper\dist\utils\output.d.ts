import { EVModel, ScrapingResult } from "../types";
/**
 * Output utilities for saving scraped data in multiple formats
 */
export declare class OutputManager {
    private outputDir;
    private timestamp;
    constructor(outputDir: string);
    /**
     * Ensure output directory exists
     */
    private ensureOutputDirectory;
    /**
     * Save data as JSON file
     */
    saveAsJSON(data: EVModel[], filename?: string): Promise<string>;
    /**
     * Save data as CSV file
     */
    saveAsCSV(data: EVModel[], filename?: string): Promise<string>;
    /**
     * Save data as SQL insert statements
     */
    saveAsSQL(data: EVModel[], filename?: string): Promise<string>;
    /**
     * Save scraping errors
     */
    saveErrors(errors: any[], filename?: string): Promise<string>;
    /**
     * Save scraping summary
     */
    saveSummary(result: ScrapingResult, filename?: string): Promise<string>;
    /**
     * Get CSV headers configuration
     */
    private getCSVHeaders;
    /**
     * Transform EV model data for CSV output
     */
    private transformForCSV;
    /**
     * Generate SQL insert statements compatible with the existing database schema
     */
    private generateSQLStatements;
    /**
     * Generate CREATE TABLE SQL statement
     */
    private getCreateTableSQL;
    /**
     * Generate INSERT SQL statement for a single EV model
     */
    private generateInsertSQL;
    /**
     * Escape SQL string values
     */
    private escapeSQLString;
}
//# sourceMappingURL=output.d.ts.map