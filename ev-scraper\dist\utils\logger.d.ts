import winston from 'winston';
export declare const logger: winston.Logger;
export declare class ProgressLogger {
    private startTime;
    private totalItems;
    private processedItems;
    private successfulItems;
    private failedItems;
    constructor(totalItems: number);
    private logStart;
    logProgress(url: string, success: boolean, details?: any): void;
    logError(url: string, error: Error, retryCount?: number): void;
    logSummary(): void;
    logCompletion(): void;
}
export declare const logScrapingStart: (config: any) => void;
export declare const logScrapingEnd: (results: any) => void;
export declare const logRateLimitDelay: (delayMs: number) => void;
export declare const logRetryAttempt: (url: string, attempt: number, maxRetries: number) => void;
//# sourceMappingURL=logger.d.ts.map