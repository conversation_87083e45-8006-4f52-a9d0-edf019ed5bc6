import { chromium, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "playwright";
import { ScrapingConfig } from "../types";
import { logger, logRateLimitDelay } from "../utils/logger";
import { siteConfig } from "../utils/config";

/**
 * Browser management for web scraping
 */
export class BrowserManager {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private config: ScrapingConfig;

  constructor(config: ScrapingConfig) {
    this.config = config;
  }

  /**
   * Initialize browser and context
   */
  async initialize(): Promise<void> {
    try {
      logger.info("Initializing browser", {
        headless: this.config.headless,
        viewport: `${this.config.viewport_width}x${this.config.viewport_height}`,
      });

      this.browser = await chromium.launch({
        headless: this.config.headless,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu",
        ],
      });

      this.context = await this.browser.newContext({
        viewport: {
          width: this.config.viewport_width,
          height: this.config.viewport_height,
        },
        userAgent: this.config.user_agent,
        extraHTTPHeaders: siteConfig.headers,
      });

      // Set up request interception for better performance
      await this.context.route("**/*", (route) => {
        const resourceType = route.request().resourceType();

        // Block unnecessary resources to speed up scraping
        if (["image", "stylesheet", "font", "media"].includes(resourceType)) {
          route.abort();
        } else {
          route.continue();
        }
      });

      logger.info("Browser initialized successfully");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to initialize browser", { error: errorMessage });
      throw error;
    }
  }

  /**
   * Create a new page with proper configuration
   */
  async createPage(): Promise<Page> {
    if (!this.context) {
      throw new Error("Browser context not initialized");
    }

    const page = await this.context.newPage();

    // Set up page event listeners
    page.on("console", (msg) => {
      if (msg.type() === "error") {
        logger.debug("Browser console error", { message: msg.text() });
      }
    });

    page.on("pageerror", (error) => {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.debug("Page error", { error: errorMessage });
    });

    page.on("requestfailed", (request) => {
      logger.debug("Request failed", {
        url: request.url(),
        failure: request.failure()?.errorText,
      });
    });

    return page;
  }

  /**
   * Navigate to URL with retry logic and rate limiting
   */
  async navigateToUrl(page: Page, url: string): Promise<boolean> {
    try {
      // Rate limiting delay
      if (this.config.delay_between_requests > 0) {
        logRateLimitDelay(this.config.delay_between_requests);
        await this.delay(this.config.delay_between_requests);
      }

      logger.debug("Navigating to URL", { url });

      const response = await page.goto(url, {
        waitUntil: "domcontentloaded",
        timeout: 30000,
      });

      if (!response) {
        logger.warn("No response received", { url });
        return false;
      }

      const status = response.status();
      if (status >= 400) {
        logger.warn("HTTP error response", { url, status });
        return false;
      }

      // Wait for page to be fully loaded
      await page
        .waitForLoadState("networkidle", { timeout: 10000 })
        .catch(() => {
          logger.debug("Network idle timeout, continuing anyway", { url });
        });

      logger.debug("Successfully navigated to URL", { url, status });
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to navigate to URL", {
        url,
        error: errorMessage,
      });
      return false;
    }
  }

  /**
   * Extract page content safely
   */
  async extractPageContent(page: Page): Promise<string> {
    try {
      return await page.content();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to extract page content", { error: errorMessage });
      return "";
    }
  }

  /**
   * Take screenshot for debugging
   */
  async takeScreenshot(page: Page, filename: string): Promise<void> {
    try {
      await page.screenshot({
        path: filename,
        fullPage: true,
      });
      logger.debug("Screenshot saved", { filename });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to take screenshot", {
        filename,
        error: errorMessage,
      });
    }
  }

  /**
   * Check if element exists on page
   */
  async elementExists(page: Page, selector: string): Promise<boolean> {
    try {
      const element = await page.$(selector);
      return element !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get text content from element
   */
  async getElementText(page: Page, selector: string): Promise<string | null> {
    try {
      const element = await page.$(selector);
      if (element) {
        return await element.textContent();
      }
      return null;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.debug("Failed to get element text", {
        selector,
        error: errorMessage,
      });
      return null;
    }
  }

  /**
   * Get attribute value from element
   */
  async getElementAttribute(
    page: Page,
    selector: string,
    attribute: string
  ): Promise<string | null> {
    try {
      const element = await page.$(selector);
      if (element) {
        return await element.getAttribute(attribute);
      }
      return null;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.debug("Failed to get element attribute", {
        selector,
        attribute,
        error: errorMessage,
      });
      return null;
    }
  }

  /**
   * Get all matching elements text
   */
  async getAllElementsText(page: Page, selector: string): Promise<string[]> {
    try {
      const elements = await page.$$(selector);
      const texts: string[] = [];

      for (const element of elements) {
        const text = await element.textContent();
        if (text) {
          texts.push(text.trim());
        }
      }

      return texts;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.debug("Failed to get all elements text", {
        selector,
        error: errorMessage,
      });
      return [];
    }
  }

  /**
   * Scroll page to load dynamic content
   */
  async scrollPage(page: Page): Promise<void> {
    try {
      await page.evaluate(() => {
        return new Promise<void>((resolve) => {
          let totalHeight = 0;
          const distance = 100;
          const timer = setInterval(() => {
            const scrollHeight = document.body.scrollHeight;
            window.scrollBy(0, distance);
            totalHeight += distance;

            if (totalHeight >= scrollHeight) {
              clearInterval(timer);
              resolve();
            }
          }, 100);
        });
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.debug("Failed to scroll page", { error: errorMessage });
    }
  }

  /**
   * Utility delay function
   */
  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Close browser and cleanup
   */
  async close(): Promise<void> {
    try {
      if (this.context) {
        await this.context.close();
        this.context = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      logger.info("Browser closed successfully");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to close browser", { error: errorMessage });
    }
  }

  /**
   * Check if browser is initialized
   */
  isInitialized(): boolean {
    return this.browser !== null && this.context !== null;
  }
}
