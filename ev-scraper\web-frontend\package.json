{"name": "ev-scraper-frontend", "version": "1.0.0", "description": "Web frontend for EV scraper data visualization", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "start": "vite"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "better-sqlite3": "^9.2.2", "express": "^4.18.2", "cors": "^2.8.5", "recharts": "^2.8.0", "@tanstack/react-table": "^8.10.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/better-sqlite3": "^7.6.8", "@vitejs/plugin-react": "^4.2.1", "typescript": "^5.2.2", "vite": "^5.0.8", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}