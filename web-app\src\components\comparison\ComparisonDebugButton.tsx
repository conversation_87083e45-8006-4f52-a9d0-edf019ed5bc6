'use client'

import { Button } from '@/components/ui/button'
import { useComparisonActions } from '@/hooks/useComparisonActions'
import { useComparison } from '@/contexts/ComparisonContext'
import { Plus, Minus, Eye } from 'lucide-react'

// Mock EV model for testing
const mockEVModel = {
  id: 'test-model-1',
  make: 'Tesla',
  model: 'Model 3',
  year: 2024,
  price_msrp: 4099900, // $40,999
  range_epa_miles: 358,
  charging_speed_dc_kw: 250,
  battery_capacity_kwh: 75,
  acceleration_0_60_mph: 4.2,
  top_speed_mph: 162,
  efficiency_mpge: 132,
  body_type: 'sedan',
  seating_capacity: 5,
  cargo_space_cu_ft: 15,
  drivetrain: 'RWD',
  production_status: 'current',
  is_featured: true,
  best_value: false,
  editor_choice: true,
  images: ['https://images.unsplash.com/photo-1617788138017-80ad40651399?w=400&h=300&fit=crop'],
  manufacturer_id: 'tesla',
  trim: 'Standard Range',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
}

const mockEVModel2 = {
  ...mockEVModel,
  id: 'test-model-2',
  make: 'BMW',
  model: 'i4',
  price_msrp: 5699500, // $56,995
  range_epa_miles: 270,
  images: ['https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=300&fit=crop']
}

export function ComparisonDebugButton() {
  const { addToComparison, removeFromComparison, clearComparison } = useComparisonActions()
  const { state, togglePanel } = useComparison()

  return (
    <div className="fixed top-4 left-4 z-[10000] flex flex-col gap-2 rounded-lg bg-white p-4 shadow-lg dark:bg-gray-800">
      <h3 className="text-sm font-semibold">Comparison Debug</h3>
      
      <div className="text-xs text-gray-600 dark:text-gray-400">
        Models: {state.models.length} | Open: {state.isOpen ? 'Yes' : 'No'}
      </div>
      
      <div className="flex gap-2">
        <Button
          size="sm"
          onClick={() => addToComparison(mockEVModel)}
          className="text-xs"
        >
          <Plus className="mr-1 h-3 w-3" />
          Add Tesla
        </Button>
        
        <Button
          size="sm"
          onClick={() => addToComparison(mockEVModel2)}
          className="text-xs"
        >
          <Plus className="mr-1 h-3 w-3" />
          Add BMW
        </Button>
      </div>
      
      <div className="flex gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={togglePanel}
          className="text-xs"
        >
          <Eye className="mr-1 h-3 w-3" />
          Toggle
        </Button>
        
        <Button
          size="sm"
          variant="destructive"
          onClick={clearComparison}
          className="text-xs"
        >
          <Minus className="mr-1 h-3 w-3" />
          Clear
        </Button>
      </div>
    </div>
  )
}
