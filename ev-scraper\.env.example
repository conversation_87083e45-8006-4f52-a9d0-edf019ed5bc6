# EV Scraper Configuration
# Copy this file to .env and adjust values as needed

# Rate Limiting (be respectful to the target website)
DELAY_BETWEEN_REQUESTS=2000
MAX_CONCURRENT_REQUESTS=3

# Retry Logic
MAX_RETRIES=3
RETRY_DELAY=5000

# Browser Settings
HEADLESS=true
USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
VIEWPORT_WIDTH=1920
VIEWPORT_HEIGHT=1080

# Output Settings
OUTPUT_DIR=./output
SAVE_JSON=true
SAVE_CSV=true
SAVE_SQL=true

# Scraping Scope (optional)
# MAX_PAGES=10
# SPECIFIC_MAKES=Tesla,BMW,Audi
# YEAR_START=2020
# YEAR_END=2024

# Logging
LOG_LEVEL=info
NODE_ENV=development
