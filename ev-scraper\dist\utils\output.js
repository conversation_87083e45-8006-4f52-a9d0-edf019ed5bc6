"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutputManager = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const csv_writer_1 = __importDefault(require("csv-writer"));
const config_1 = require("./config");
const logger_1 = require("./logger");
/**
 * Output utilities for saving scraped data in multiple formats
 */
class OutputManager {
    outputDir;
    timestamp;
    constructor(outputDir) {
        this.outputDir = outputDir;
        this.timestamp = config_1.outputConfig.timestamp();
        this.ensureOutputDirectory();
    }
    /**
     * Ensure output directory exists
     */
    async ensureOutputDirectory() {
        try {
            await fs_extra_1.default.ensureDir(this.outputDir);
            logger_1.logger.info('Output directory ready', { outputDir: this.outputDir });
        }
        catch (error) {
            logger_1.logger.error('Failed to create output directory', {
                outputDir: this.outputDir,
                error: error.message
            });
            throw error;
        }
    }
    /**
     * Save data as JSON file
     */
    async saveAsJSON(data, filename) {
        const fileName = filename || config_1.outputConfig.fileNames.json(this.timestamp);
        const filePath = path_1.default.join(this.outputDir, fileName);
        try {
            const jsonData = {
                metadata: {
                    scraped_at: new Date().toISOString(),
                    total_vehicles: data.length,
                    version: '1.0.0'
                },
                vehicles: data
            };
            await fs_extra_1.default.writeJSON(filePath, jsonData, { spaces: 2 });
            logger_1.logger.info('JSON file saved', { filePath, count: data.length });
            return filePath;
        }
        catch (error) {
            logger_1.logger.error('Failed to save JSON file', {
                filePath,
                error: error.message
            });
            throw error;
        }
    }
    /**
     * Save data as CSV file
     */
    async saveAsCSV(data, filename) {
        const fileName = filename || config_1.outputConfig.fileNames.csv(this.timestamp);
        const filePath = path_1.default.join(this.outputDir, fileName);
        try {
            const csvWriter = csv_writer_1.default.createObjectCsvWriter({
                path: filePath,
                header: this.getCSVHeaders()
            });
            const csvData = data.map(this.transformForCSV);
            await csvWriter.writeRecords(csvData);
            logger_1.logger.info('CSV file saved', { filePath, count: data.length });
            return filePath;
        }
        catch (error) {
            logger_1.logger.error('Failed to save CSV file', {
                filePath,
                error: error.message
            });
            throw error;
        }
    }
    /**
     * Save data as SQL insert statements
     */
    async saveAsSQL(data, filename) {
        const fileName = filename || config_1.outputConfig.fileNames.sql(this.timestamp);
        const filePath = path_1.default.join(this.outputDir, fileName);
        try {
            const sqlStatements = this.generateSQLStatements(data);
            await fs_extra_1.default.writeFile(filePath, sqlStatements, 'utf8');
            logger_1.logger.info('SQL file saved', { filePath, count: data.length });
            return filePath;
        }
        catch (error) {
            logger_1.logger.error('Failed to save SQL file', {
                filePath,
                error: error.message
            });
            throw error;
        }
    }
    /**
     * Save scraping errors
     */
    async saveErrors(errors, filename) {
        const fileName = filename || config_1.outputConfig.fileNames.errors(this.timestamp);
        const filePath = path_1.default.join(this.outputDir, fileName);
        try {
            const errorData = {
                metadata: {
                    scraped_at: new Date().toISOString(),
                    total_errors: errors.length
                },
                errors
            };
            await fs_extra_1.default.writeJSON(filePath, errorData, { spaces: 2 });
            logger_1.logger.info('Errors file saved', { filePath, count: errors.length });
            return filePath;
        }
        catch (error) {
            logger_1.logger.error('Failed to save errors file', {
                filePath,
                error: error.message
            });
            throw error;
        }
    }
    /**
     * Save scraping summary
     */
    async saveSummary(result, filename) {
        const fileName = filename || config_1.outputConfig.fileNames.summary(this.timestamp);
        const filePath = path_1.default.join(this.outputDir, fileName);
        try {
            await fs_extra_1.default.writeJSON(filePath, result, { spaces: 2 });
            logger_1.logger.info('Summary file saved', { filePath });
            return filePath;
        }
        catch (error) {
            logger_1.logger.error('Failed to save summary file', {
                filePath,
                error: error.message
            });
            throw error;
        }
    }
    /**
     * Get CSV headers configuration
     */
    getCSVHeaders() {
        return [
            { id: 'make', title: 'Make' },
            { id: 'model', title: 'Model' },
            { id: 'year', title: 'Year' },
            { id: 'trim', title: 'Trim' },
            { id: 'price_msrp', title: 'Price MSRP (cents)' },
            { id: 'battery_capacity_kwh', title: 'Battery Capacity (kWh)' },
            { id: 'range_epa_miles', title: 'EPA Range (miles)' },
            { id: 'charging_speed_dc_kw', title: 'DC Charging Speed (kW)' },
            { id: 'charging_speed_ac_kw', title: 'AC Charging Speed (kW)' },
            { id: 'acceleration_0_60_mph', title: '0-60 mph (seconds)' },
            { id: 'top_speed_mph', title: 'Top Speed (mph)' },
            { id: 'efficiency_mpge', title: 'Efficiency (MPGe)' },
            { id: 'body_type', title: 'Body Type' },
            { id: 'seating_capacity', title: 'Seating Capacity' },
            { id: 'drivetrain', title: 'Drivetrain' },
            { id: 'motor_power_hp', title: 'Motor Power (HP)' },
            { id: 'motor_power_kw', title: 'Motor Power (kW)' },
            { id: 'production_status', title: 'Production Status' },
            { id: 'source_url', title: 'Source URL' },
            { id: 'scraped_at', title: 'Scraped At' }
        ];
    }
    /**
     * Transform EV model data for CSV output
     */
    transformForCSV(model) {
        return {
            make: model.make || '',
            model: model.model || '',
            year: model.year || '',
            trim: model.trim || '',
            price_msrp: model.price_msrp || '',
            battery_capacity_kwh: model.battery_capacity_kwh || '',
            range_epa_miles: model.range_epa_miles || '',
            charging_speed_dc_kw: model.charging_speed_dc_kw || '',
            charging_speed_ac_kw: model.charging_speed_ac_kw || '',
            acceleration_0_60_mph: model.acceleration_0_60_mph || '',
            top_speed_mph: model.top_speed_mph || '',
            efficiency_mpge: model.efficiency_mpge || '',
            body_type: model.body_type || '',
            seating_capacity: model.seating_capacity || '',
            drivetrain: model.drivetrain || '',
            motor_power_hp: model.motor_power_hp || '',
            motor_power_kw: model.motor_power_kw || '',
            production_status: model.production_status || '',
            source_url: model.source_url || '',
            scraped_at: model.scraped_at || ''
        };
    }
    /**
     * Generate SQL insert statements compatible with the existing database schema
     */
    generateSQLStatements(data) {
        const statements = [];
        // Add header comment
        statements.push('-- Electric Vehicle Data Import');
        statements.push(`-- Generated on: ${new Date().toISOString()}`);
        statements.push(`-- Total records: ${data.length}`);
        statements.push('');
        // Add table creation statement (commented out by default)
        statements.push('-- Uncomment the following to create the table if it doesn\'t exist:');
        statements.push('-- ' + this.getCreateTableSQL());
        statements.push('');
        // Generate insert statements
        statements.push('-- Insert statements:');
        statements.push('BEGIN TRANSACTION;');
        statements.push('');
        for (const model of data) {
            const sql = this.generateInsertSQL(model);
            if (sql) {
                statements.push(sql);
            }
        }
        statements.push('');
        statements.push('COMMIT;');
        return statements.join('\n');
    }
    /**
     * Generate CREATE TABLE SQL statement
     */
    getCreateTableSQL() {
        return `
CREATE TABLE IF NOT EXISTS ev_models (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  make VARCHAR(100) NOT NULL,
  model VARCHAR(200) NOT NULL,
  year INTEGER NOT NULL,
  trim VARCHAR(200),
  price_msrp BIGINT,
  battery_capacity_kwh DECIMAL(6,2) NOT NULL,
  range_epa_miles INTEGER,
  charging_speed_dc_kw DECIMAL(6,2),
  charging_speed_ac_kw DECIMAL(6,2),
  acceleration_0_60_mph DECIMAL(4,2),
  top_speed_mph INTEGER,
  efficiency_mpge INTEGER,
  body_type VARCHAR(50),
  seating_capacity INTEGER,
  cargo_space_cu_ft DECIMAL(6,2),
  drivetrain VARCHAR(10),
  motor_power_kw DECIMAL(8,2),
  motor_power_hp DECIMAL(8,2),
  production_status VARCHAR(20) DEFAULT 'current',
  is_featured BOOLEAN DEFAULT FALSE,
  best_value BOOLEAN DEFAULT FALSE,
  editor_choice BOOLEAN DEFAULT FALSE,
  images TEXT[],
  manufacturer_id VARCHAR(100),
  source_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`.trim();
    }
    /**
     * Generate INSERT SQL statement for a single EV model
     */
    generateInsertSQL(model) {
        try {
            const fields = [];
            const values = [];
            // Required fields
            if (!model.make || !model.model || !model.year || !model.battery_capacity_kwh) {
                return null; // Skip incomplete records
            }
            fields.push('make', 'model', 'year', 'battery_capacity_kwh');
            values.push(this.escapeSQLString(model.make), this.escapeSQLString(model.model), model.year.toString(), model.battery_capacity_kwh.toString());
            // Optional fields
            const optionalFields = [
                { field: 'trim', value: model.trim },
                { field: 'price_msrp', value: model.price_msrp },
                { field: 'range_epa_miles', value: model.range_epa_miles },
                { field: 'charging_speed_dc_kw', value: model.charging_speed_dc_kw },
                { field: 'charging_speed_ac_kw', value: model.charging_speed_ac_kw },
                { field: 'acceleration_0_60_mph', value: model.acceleration_0_60_mph },
                { field: 'top_speed_mph', value: model.top_speed_mph },
                { field: 'efficiency_mpge', value: model.efficiency_mpge },
                { field: 'body_type', value: model.body_type },
                { field: 'seating_capacity', value: model.seating_capacity },
                { field: 'drivetrain', value: model.drivetrain },
                { field: 'motor_power_kw', value: model.motor_power_kw },
                { field: 'motor_power_hp', value: model.motor_power_hp },
                { field: 'production_status', value: model.production_status },
                { field: 'source_url', value: model.source_url }
            ];
            for (const { field, value } of optionalFields) {
                if (value !== undefined && value !== null) {
                    fields.push(field);
                    if (typeof value === 'string') {
                        values.push(this.escapeSQLString(value));
                    }
                    else {
                        values.push(value.toString());
                    }
                }
            }
            // Handle arrays (images)
            if (model.images && model.images.length > 0) {
                fields.push('images');
                const imageArray = model.images.map(img => this.escapeSQLString(img)).join(',');
                values.push(`ARRAY[${imageArray}]`);
            }
            return `INSERT INTO ev_models (${fields.join(', ')}) VALUES (${values.join(', ')});`;
        }
        catch (error) {
            logger_1.logger.error('Failed to generate SQL for model', {
                make: model.make,
                model: model.model,
                error: error.message
            });
            return null;
        }
    }
    /**
     * Escape SQL string values
     */
    escapeSQLString(value) {
        if (!value)
            return 'NULL';
        return `'${value.replace(/'/g, "''")}'`;
    }
}
exports.OutputManager = OutputManager;
//# sourceMappingURL=output.js.map