'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Search, 
  ArrowRight, 
  DollarSign, 
  MapPin, 
  Users, 
  Car,
  Zap
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface EVFinderFormData {
  budget: number[]
  useCase: string
  rangeRequirement: number[]
  bodyType: string
}

export function EVFinderSection() {
  const router = useRouter()
  const [formData, setFormData] = useState<EVFinderFormData>({
    budget: [50000],
    useCase: '',
    rangeRequirement: [250],
    bodyType: ''
  })

  const useCases = [
    { value: 'commuting', label: 'Daily Commuting', description: 'Efficient city driving' },
    { value: 'family', label: 'Family Use', description: 'Spacious and safe' },
    { value: 'performance', label: 'Performance', description: 'Speed and handling' },
    { value: 'luxury', label: 'Luxury', description: 'Premium features' },
    { value: 'utility', label: 'Work/Utility', description: 'Cargo and towing' },
    { value: 'eco_friendly', label: 'Eco-Friendly', description: 'Maximum efficiency' }
  ]

  const bodyTypes = [
    { value: 'sedan', label: 'Sedan' },
    { value: 'suv', label: 'SUV' },
    { value: 'hatchback', label: 'Hatchback' },
    { value: 'truck', label: 'Truck' },
    { value: 'coupe', label: 'Coupe' },
    { value: 'crossover', label: 'Crossover' }
  ]

  const handleFindEVs = () => {
    const params = new URLSearchParams()
    
    // Add budget filter
    if (formData.budget[0] < 100000) {
      params.set('priceMax', (formData.budget[0] * 100).toString()) // Convert to cents
    }
    
    // Add range filter
    if (formData.rangeRequirement[0] > 200) {
      params.set('rangeMin', formData.rangeRequirement[0].toString())
    }
    
    // Add body type filter
    if (formData.bodyType) {
      params.set('bodyType', formData.bodyType)
    }
    
    // Add use case as search query
    if (formData.useCase) {
      const useCaseLabel = useCases.find(uc => uc.value === formData.useCase)?.label
      if (useCaseLabel) {
        params.set('search', `${useCaseLabel} electric vehicle`)
      }
    }
    
    router.push(`/ev-models?${params.toString()}`)
  }

  const getRecommendationPreview = () => {
    const budget = formData.budget[0]
    const range = formData.rangeRequirement[0]
    const useCase = useCases.find(uc => uc.value === formData.useCase)
    
    let recommendations = []
    
    if (budget < 40000 && range < 300) {
      recommendations.push('Nissan Leaf', 'Chevrolet Bolt EV')
    } else if (budget > 70000 && formData.useCase === 'luxury') {
      recommendations.push('BMW iX', 'Mercedes EQS')
    } else if (formData.bodyType === 'suv' && formData.useCase === 'family') {
      recommendations.push('Ford Mustang Mach-E', 'Hyundai IONIQ 5')
    } else {
      recommendations.push('Tesla Model 3', 'BMW i4')
    }
    
    return recommendations.slice(0, 2)
  }

  const recommendations = getRecommendationPreview()

  return (
    <section className="bg-gradient-to-br from-electric-50 to-blue-50 py-16 dark:from-gray-800 dark:to-gray-900">
      <div className="container">
        <div className="mx-auto max-w-4xl">
          {/* Section Header */}
          <div className="mb-12 text-center">
            <Badge variant="secondary" className="mb-4">
              <Search className="mr-1 h-3 w-3" />
              EV Finder
            </Badge>
            <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl">
              Find Your Perfect EV
            </h2>
            <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
              Answer a few quick questions and get personalized EV recommendations
            </p>
          </div>

          <div className="grid gap-8 lg:grid-cols-3">
            {/* Finder Form */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car className="h-5 w-5 text-electric-600" />
                    Tell us about your needs
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Budget */}
                  <div className="space-y-3">
                    <Label className="flex items-center gap-2 text-base font-semibold">
                      <DollarSign className="h-4 w-4" />
                      Budget Range
                    </Label>
                    <div className="px-3">
                      <Slider
                        value={formData.budget}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, budget: value }))}
                        max={100000}
                        min={20000}
                        step={5000}
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <span>$20k</span>
                        <span className="font-semibold">Up to ${formData.budget[0].toLocaleString()}</span>
                        <span>$100k+</span>
                      </div>
                    </div>
                  </div>

                  {/* Use Case */}
                  <div className="space-y-3">
                    <Label className="text-base font-semibold">Primary Use Case</Label>
                    <RadioGroup 
                      value={formData.useCase} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, useCase: value }))}
                      className="grid grid-cols-2 gap-3"
                    >
                      {useCases.map((useCase) => (
                        <div key={useCase.value} className="flex items-center space-x-2">
                          <RadioGroupItem value={useCase.value} id={useCase.value} />
                          <Label htmlFor={useCase.value} className="text-sm">
                            <div>
                              <div className="font-medium">{useCase.label}</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">
                                {useCase.description}
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>

                  {/* Range Requirement */}
                  <div className="space-y-3">
                    <Label className="flex items-center gap-2 text-base font-semibold">
                      <MapPin className="h-4 w-4" />
                      Minimum Range Needed
                    </Label>
                    <div className="px-3">
                      <Slider
                        value={formData.rangeRequirement}
                        onValueChange={(value) => setFormData(prev => ({ ...prev, rangeRequirement: value }))}
                        max={400}
                        min={150}
                        step={25}
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <span>150 mi</span>
                        <span className="font-semibold">{formData.rangeRequirement[0]} miles</span>
                        <span>400+ mi</span>
                      </div>
                    </div>
                  </div>

                  {/* Body Type */}
                  <div className="space-y-3">
                    <Label className="flex items-center gap-2 text-base font-semibold">
                      <Users className="h-4 w-4" />
                      Preferred Body Type
                    </Label>
                    <Select value={formData.bodyType} onValueChange={(value) => setFormData(prev => ({ ...prev, bodyType: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select body type (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        {bodyTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Find Button */}
                  <Button 
                    onClick={handleFindEVs}
                    className="w-full bg-electric-600 hover:bg-electric-700 group"
                    size="lg"
                  >
                    <Search className="mr-2 h-4 w-4" />
                    Find My Perfect EVs
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Preview Results */}
            <div>
              <Card className="border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-electric-600" />
                    Preview Matches
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recommendations.length > 0 ? (
                    <>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Based on your preferences, you might like:
                      </p>
                      <div className="space-y-3">
                        {recommendations.map((rec, index) => (
                          <div key={index} className="rounded-lg border p-3 bg-gray-50 dark:bg-gray-800">
                            <div className="font-medium text-sm">{rec}</div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              Great match for your needs
                            </div>
                          </div>
                        ))}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-500">
                        Click "Find My Perfect EVs" to see all matches with detailed comparisons
                      </p>
                    </>
                  ) : (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Select your preferences to see personalized recommendations
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
