'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/ThemeToggle'
import { ComparisonToggle } from '@/components/comparison/ComparisonToggle'
import { UserProfileDropdown } from '@/components/UserProfileDropdown'
import { Zap, Menu, X } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'

interface HeaderProps {
  variant?: 'default' | 'dashboard'
}

export function Header({ variant = 'default' }: HeaderProps) {
  const { user } = useAuth()
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  const isActive = (path: string) => {
    return pathname === path
  }

  // Main navigation links for authenticated users (user-specific items moved to dropdown)
  const authenticatedNavLinks = [
    { href: '/ev-models', label: 'Browse EVs' },
    { href: '/compare', label: 'Compare' },
    { href: '/forum', label: 'Community' },
  ]

  // Navigation links for public pages
  const publicNavLinks = [
    { href: '/', label: 'Home' },
    { href: '/ev-models', label: 'Browse EVs' },
    { href: '/compare', label: 'Compare' },
    { href: '/forum', label: 'Community' },
    { href: '/#features', label: 'Features' },
    { href: '/#about', label: 'About' },
  ]

  const navLinks = user ? authenticatedNavLinks : publicNavLinks

  if (variant === 'dashboard') {
    return (
      <header className="border-b border-gray-200 bg-white dark:border-gray-800 dark:bg-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <Link href={'/'} className="flex items-center">
              <Zap className="mr-2 h-8 w-8 text-electric-600" />
              <span className="text-xl font-bold text-gray-900 dark:text-white">GreenMilesEV</span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden items-center space-x-6 md:flex">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`text-sm font-medium transition-colors ${
                    isActive(link.href)
                      ? 'text-electric-600'
                      : 'text-gray-600 hover:text-electric-600 dark:text-gray-300'
                  }`}
                >
                  {link.label}
                </Link>
              ))}
            </nav>

            {/* User Actions */}
            <div className="flex items-center space-x-3">
              {user ? (
                <>
                  <ComparisonToggle />
                  <ThemeToggle />
                  <UserProfileDropdown variant="dashboard" />
                </>
              ) : (
                <>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href="/auth/signin">Sign In</Link>
                  </Button>
                  <Button size="sm" className="bg-electric-600 hover:bg-electric-700" asChild>
                    <Link href="/auth/signup">Get Started</Link>
                  </Button>
                </>
              )}

              {/* Mobile menu button */}
              <Button variant="ghost" size="sm" className="md:hidden" onClick={toggleMobileMenu}>
                {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <div className="border-t bg-white dark:bg-gray-900 md:hidden">
              <nav className="space-y-2 px-4 py-4">
                {/* Main Navigation Links */}
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`block rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                      isActive(link.href)
                        ? 'bg-electric-50 text-electric-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-electric-600'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}

                {/* User-specific links for mobile (if authenticated) */}
                {user && (
                  <>
                    <div className="my-3 border-t border-gray-200 dark:border-gray-700"></div>
                    <div className="px-3 py-2 text-xs font-semibold uppercase tracking-wider text-gray-500">
                      Account
                    </div>
                    {[
                      { href: '/dashboard', label: 'Dashboard' },
                      { href: '/profile', label: 'Profile' },
                      { href: '/vehicles', label: 'My Vehicles' },
                      { href: '/charging', label: 'Charging' },
                      { href: '/analytics', label: 'Analytics' },
                      { href: '/settings', label: 'Settings' },
                    ].map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className={`block rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                          isActive(link.href)
                            ? 'bg-electric-50 text-electric-600'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-electric-600'
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {link.label}
                      </Link>
                    ))}
                  </>
                )}
              </nav>
            </div>
          )}
        </div>
      </header>
    )
  }

  // Default header for public pages
  return (
    <header className="border-b border-gray-200 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:border-gray-800 dark:bg-gray-900/95 dark:supports-[backdrop-filter]:bg-gray-900/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Zap className="h-6 w-6 text-electric-600" />
          <span className="text-xl font-bold text-gray-900 dark:text-white">GreenMilesEV</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden items-center space-x-6 md:flex">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              href={link.href}
              className={`text-sm font-medium transition-colors ${
                isActive(link.href)
                  ? 'text-electric-600'
                  : 'text-gray-600 hover:text-electric-600 dark:text-gray-300'
              }`}
            >
              {link.label}
            </Link>
          ))}
        </nav>

        {/* Auth Actions */}
        <div className="flex items-center space-x-3">
          <ComparisonToggle />
          <ThemeToggle />
          {user ? (
            <UserProfileDropdown variant="default" />
          ) : (
            <>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/auth/signin">Sign In</Link>
              </Button>
              <Button size="sm" className="bg-electric-600 hover:bg-electric-700" asChild>
                <Link href="/auth/signup">Get Started</Link>
              </Button>
            </>
          )}

          {/* Mobile menu button */}
          <Button variant="ghost" size="sm" className="md:hidden" onClick={toggleMobileMenu}>
            {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="border-t bg-white dark:bg-gray-900 md:hidden">
          <nav className="container space-y-2 px-4 py-4">
            {/* Main Navigation Links */}
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`block rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                  isActive(link.href)
                    ? 'bg-electric-50 text-electric-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-electric-600'
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}

            {/* User-specific links for mobile (if authenticated) */}
            {user && (
              <>
                <div className="my-3 border-t border-gray-200 dark:border-gray-700"></div>
                <div className="px-3 py-2 text-xs font-semibold uppercase tracking-wider text-gray-500">
                  Account
                </div>
                {[
                  { href: '/dashboard', label: 'Dashboard' },
                  { href: '/profile', label: 'Profile' },
                  { href: '/vehicles', label: 'My Vehicles' },
                  { href: '/charging', label: 'Charging' },
                  { href: '/analytics', label: 'Analytics' },
                  { href: '/settings', label: 'Settings' },
                ].map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`block rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                      isActive(link.href)
                        ? 'bg-electric-50 text-electric-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-electric-600'
                    }`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}
              </>
            )}
          </nav>
        </div>
      )}
    </header>
  )
}
