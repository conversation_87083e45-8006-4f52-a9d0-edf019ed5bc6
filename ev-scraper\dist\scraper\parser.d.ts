import { EVModel } from "../types";
/**
 * HTML parsing utilities for extracting EV data
 */
export declare class EVDataParser {
    private $;
    constructor(html: string);
    /**
     * Extract EV model data from a detail page
     */
    extractEVModel(url: string): Partial<EVModel>;
    /**
     * Extract basic vehicle information
     */
    private extractBasicInfo;
    /**
     * Extract technical specifications from spec tables
     */
    private extractSpecifications;
    /**
     * Parse individual specification rows
     */
    private parseSpecificationRow;
    /**
     * Extract specifications using direct selectors
     */
    private extractDirectSpecs;
    /**
     * Extract pricing information
     */
    private extractPricing;
    /**
     * Extract performance data
     */
    private extractPerformance;
    /**
     * Extract efficiency data
     */
    private extractEfficiency;
    /**
     * Extract physical specifications
     */
    private extractPhysicalSpecs;
    /**
     * Extract vehicle images
     */
    private extractImages;
    /**
     * Extract car links from listing pages
     */
    extractCarLinks(): string[];
    /**
     * Check if URL is a valid car detail page
     */
    private isValidCarUrl;
    /**
     * Resolve relative URLs
     */
    private resolveUrl;
    /**
     * Extract additional metadata
     */
    private extractMetadata;
    /**
     * Utility methods
     */
    private getTextContent;
    private parseTitleString;
    private parseDrivetrain;
    private resolveImageUrl;
}
//# sourceMappingURL=parser.d.ts.map