import { Page } from 'playwright';
import { ScrapingConfig } from '../types';
/**
 * Browser management for web scraping
 */
export declare class BrowserManager {
    private browser;
    private context;
    private config;
    constructor(config: ScrapingConfig);
    /**
     * Initialize browser and context
     */
    initialize(): Promise<void>;
    /**
     * Create a new page with proper configuration
     */
    createPage(): Promise<Page>;
    /**
     * Navigate to URL with retry logic and rate limiting
     */
    navigateToUrl(page: Page, url: string): Promise<boolean>;
    /**
     * Extract page content safely
     */
    extractPageContent(page: Page): Promise<string>;
    /**
     * Take screenshot for debugging
     */
    takeScreenshot(page: Page, filename: string): Promise<void>;
    /**
     * Check if element exists on page
     */
    elementExists(page: Page, selector: string): Promise<boolean>;
    /**
     * Get text content from element
     */
    getElementText(page: Page, selector: string): Promise<string | null>;
    /**
     * Get attribute value from element
     */
    getElementAttribute(page: Page, selector: string, attribute: string): Promise<string | null>;
    /**
     * Get all matching elements text
     */
    getAllElementsText(page: Page, selector: string): Promise<string[]>;
    /**
     * Scroll page to load dynamic content
     */
    scrollPage(page: Page): Promise<void>;
    /**
     * Utility delay function
     */
    private delay;
    /**
     * Close browser and cleanup
     */
    close(): Promise<void>;
    /**
     * Check if browser is initialized
     */
    isInitialized(): boolean;
}
//# sourceMappingURL=browser.d.ts.map