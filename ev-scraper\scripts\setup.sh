#!/bin/bash

# EV Scraper Setup Script
# This script sets up the development environment

set -e

echo "🚗 Setting up EV Scraper..."

# Check Node.js version
echo "📋 Checking Node.js version..."
node_version=$(node --version | cut -d'v' -f2)
required_version="18.0.0"

if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Node.js version $node_version is too old. Please install Node.js $required_version or higher."
    exit 1
fi

echo "✅ Node.js version $node_version is compatible"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
npx playwright install chromium

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️  Creating environment configuration..."
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
else
    echo "⚙️  Environment file already exists"
fi

# Create output directory
echo "📁 Creating output directory..."
mkdir -p output
mkdir -p logs
mkdir -p examples/output

# Build the project
echo "🔨 Building project..."
npm run build

# Run type checking
echo "🔍 Running type check..."
npm run type-check

# Run linting
echo "🧹 Running linter..."
npm run lint || echo "⚠️  Linting issues found (non-critical)"

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "🚀 Quick start:"
echo "   npm run scrape:dev --help    # Show all options"
echo "   npm run scrape:dev           # Run with default settings"
echo "   npm run scrape:dev -- --makes \"Tesla\" --max-pages 2"
echo ""
echo "📚 Documentation:"
echo "   cat README.md                # Full documentation"
echo "   ls examples/                 # Usage examples"
echo ""
echo "🔧 Configuration:"
echo "   edit .env                    # Environment variables"
echo "   edit src/utils/config.ts     # Advanced configuration"
echo ""
