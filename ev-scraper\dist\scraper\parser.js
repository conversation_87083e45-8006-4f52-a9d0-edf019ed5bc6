"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.EVDataParser = void 0;
const cheerio = __importStar(require("cheerio"));
const config_1 = require("../utils/config");
const validation_1 = require("../utils/validation");
const logger_1 = require("../utils/logger");
/**
 * HTML parsing utilities for extracting EV data
 */
class EVDataParser {
    $;
    constructor(html) {
        this.$ = cheerio.load(html);
    }
    /**
     * Extract EV model data from a detail page
     */
    extractEVModel(url) {
        const data = {
            source_url: url,
            scraped_at: new Date().toISOString(),
        };
        try {
            // Extract basic information
            this.extractBasicInfo(data);
            // Extract specifications
            this.extractSpecifications(data);
            // Extract pricing
            this.extractPricing(data);
            // Extract performance data
            this.extractPerformance(data);
            // Extract efficiency data
            this.extractEfficiency(data);
            // Extract physical specifications
            this.extractPhysicalSpecs(data);
            // Extract images
            this.extractImages(data);
            // Extract additional metadata
            this.extractMetadata(data);
            logger_1.logger.debug("Extracted EV data", {
                url,
                make: data.make,
                model: data.model,
                fieldsExtracted: Object.keys(data).length,
            });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to extract EV data", {
                url,
                error: errorMessage,
            });
        }
        return data;
    }
    /**
     * Extract basic vehicle information
     */
    extractBasicInfo(data) {
        // Extract title and parse make/model
        const title = this.getTextContent(config_1.siteConfig.selectors.carTitle);
        if (title) {
            const titleParts = this.parseTitleString(title);
            data.make = titleParts.make;
            data.model = titleParts.model;
            data.year = titleParts.year;
            data.trim = titleParts.trim;
        }
        // Try alternative selectors for make/model
        if (!data.make || !data.model) {
            data.make =
                data.make ||
                    this.getTextContent(".make, .brand, .manufacturer") ||
                    undefined;
            data.model =
                data.model ||
                    this.getTextContent(".model, .vehicle-model") ||
                    undefined;
        }
        // Extract year if not found in title
        if (!data.year) {
            const yearText = this.getTextContent(".year, .model-year");
            if (yearText) {
                const yearMatch = yearText.match(/20\d{2}/);
                if (yearMatch) {
                    data.year = parseInt(yearMatch[0]);
                }
            }
        }
        // Extract body type
        data.body_type = this.getTextContent(config_1.siteConfig.selectors.bodyType)?.toLowerCase();
        // Extract drivetrain
        const drivetrainText = this.getTextContent(config_1.siteConfig.selectors.drivetrain);
        if (drivetrainText) {
            data.drivetrain = this.parseDrivetrain(drivetrainText);
        }
        // Extract seating capacity
        const seatingText = this.getTextContent(config_1.siteConfig.selectors.seating);
        if (seatingText) {
            const seatingMatch = seatingText.match(/(\d+)/);
            if (seatingMatch) {
                data.seating_capacity = parseInt(seatingMatch[1]);
            }
        }
    }
    /**
     * Extract technical specifications from spec tables
     */
    extractSpecifications(data) {
        // Look for specification tables
        const specTables = this.$(config_1.siteConfig.selectors.specTable);
        specTables.each((_, table) => {
            const $table = this.$(table);
            const rows = $table.find(config_1.siteConfig.selectors.specRows);
            rows.each((_, row) => {
                const $row = this.$(row);
                const label = $row
                    .find(config_1.siteConfig.selectors.specLabel)
                    .text()
                    .trim()
                    .toLowerCase();
                const value = $row.find(config_1.siteConfig.selectors.specValue).text().trim();
                if (!label || !value)
                    return;
                this.parseSpecificationRow(label, value, data);
            });
        });
        // Try direct selectors for common specs
        this.extractDirectSpecs(data);
    }
    /**
     * Parse individual specification rows
     */
    parseSpecificationRow(label, value, data) {
        // Battery capacity
        if (label.includes("battery") && label.includes("capacity")) {
            const capacity = (0, validation_1.extractNumericValue)(value, config_1.siteConfig.patterns.battery);
            if (capacity)
                data.battery_capacity_kwh = capacity;
        }
        // Range
        if (label.includes("range") || label.includes("epa")) {
            const range = (0, validation_1.parseRange)(value);
            if (range)
                data.range_epa_miles = range;
        }
        // Charging speed
        if (label.includes("charging") &&
            (label.includes("dc") || label.includes("fast"))) {
            const charging = (0, validation_1.extractNumericValue)(value, config_1.siteConfig.patterns.charging);
            if (charging)
                data.charging_speed_dc_kw = charging;
        }
        if (label.includes("charging") && label.includes("ac")) {
            const charging = (0, validation_1.extractNumericValue)(value, config_1.siteConfig.patterns.charging);
            if (charging)
                data.charging_speed_ac_kw = charging;
        }
        // Acceleration
        if (label.includes("0-60") || label.includes("acceleration")) {
            const accel = (0, validation_1.extractNumericValue)(value, config_1.siteConfig.patterns.acceleration);
            if (accel)
                data.acceleration_0_60_mph = accel;
        }
        // Top speed
        if (label.includes("top speed") || label.includes("max speed")) {
            const speed = (0, validation_1.extractNumericValue)(value, config_1.siteConfig.patterns.speed);
            if (speed)
                data.top_speed_mph = speed;
        }
        // Efficiency
        if (label.includes("mpge") || label.includes("efficiency")) {
            const efficiency = (0, validation_1.extractNumericValue)(value, config_1.siteConfig.patterns.efficiency);
            if (efficiency)
                data.efficiency_mpge = efficiency;
        }
        // Power
        if (label.includes("power") || label.includes("motor")) {
            const powerMatch = value.match(config_1.siteConfig.patterns.power);
            if (powerMatch) {
                const power = parseFloat(powerMatch[1]);
                const unit = powerMatch[2].toLowerCase();
                if (unit === "hp") {
                    data.motor_power_hp = power;
                    data.motor_power_kw = Math.round(power * 0.746); // Convert HP to kW
                }
                else if (unit === "kw") {
                    data.motor_power_kw = power;
                    data.motor_power_hp = Math.round(power * 1.341); // Convert kW to HP
                }
            }
        }
    }
    /**
     * Extract specifications using direct selectors
     */
    extractDirectSpecs(data) {
        // Battery
        if (!data.battery_capacity_kwh) {
            const batteryText = this.getTextContent(config_1.siteConfig.selectors.battery);
            if (batteryText) {
                const capacity = (0, validation_1.extractNumericValue)(batteryText, config_1.siteConfig.patterns.battery);
                if (capacity)
                    data.battery_capacity_kwh = capacity;
            }
        }
        // Range
        if (!data.range_epa_miles) {
            const rangeText = this.getTextContent(config_1.siteConfig.selectors.range);
            if (rangeText) {
                const range = (0, validation_1.parseRange)(rangeText);
                if (range)
                    data.range_epa_miles = range;
            }
        }
        // Charging
        if (!data.charging_speed_dc_kw) {
            const chargingText = this.getTextContent(config_1.siteConfig.selectors.charging);
            if (chargingText) {
                const charging = (0, validation_1.extractNumericValue)(chargingText, config_1.siteConfig.patterns.charging);
                if (charging)
                    data.charging_speed_dc_kw = charging;
            }
        }
    }
    /**
     * Extract pricing information
     */
    extractPricing(data) {
        const priceText = this.getTextContent(config_1.siteConfig.selectors.price);
        if (priceText) {
            const price = (0, validation_1.parsePrice)(priceText);
            if (price) {
                data.price_msrp = price;
            }
        }
    }
    /**
     * Extract performance data
     */
    extractPerformance(data) {
        const performanceText = this.getTextContent(config_1.siteConfig.selectors.performance);
        if (performanceText) {
            // Try to extract acceleration
            const accel = (0, validation_1.extractNumericValue)(performanceText, config_1.siteConfig.patterns.acceleration);
            if (accel)
                data.acceleration_0_60_mph = accel;
            // Try to extract top speed
            const speed = (0, validation_1.extractNumericValue)(performanceText, config_1.siteConfig.patterns.speed);
            if (speed)
                data.top_speed_mph = speed;
        }
    }
    /**
     * Extract efficiency data
     */
    extractEfficiency(data) {
        const efficiencyText = this.getTextContent(config_1.siteConfig.selectors.efficiency);
        if (efficiencyText) {
            const efficiency = (0, validation_1.extractNumericValue)(efficiencyText, config_1.siteConfig.patterns.efficiency);
            if (efficiency)
                data.efficiency_mpge = efficiency;
        }
    }
    /**
     * Extract physical specifications
     */
    extractPhysicalSpecs(data) {
        // This would be expanded based on the actual site structure
        // Look for dimensions, weight, cargo space, etc.
    }
    /**
     * Extract vehicle images
     */
    extractImages(data) {
        const images = [];
        // Main image
        const mainImageSrc = this.$(config_1.siteConfig.selectors.mainImage).attr("src");
        if (mainImageSrc) {
            images.push(this.resolveImageUrl(mainImageSrc));
        }
        // Gallery images
        this.$(config_1.siteConfig.selectors.galleryImages).each((_, img) => {
            const src = this.$(img).attr("src");
            if (src) {
                images.push(this.resolveImageUrl(src));
            }
        });
        if (images.length > 0) {
            data.images = [...new Set(images)]; // Remove duplicates
        }
    }
    /**
     * Extract car links from listing pages
     */
    extractCarLinks() {
        const links = [];
        this.$(config_1.siteConfig.selectors.carLinks).each((_, link) => {
            const href = this.$(link).attr("href");
            if (href) {
                const fullUrl = this.resolveUrl(href);
                if (this.isValidCarUrl(fullUrl)) {
                    links.push(fullUrl);
                }
            }
        });
        return [...new Set(links)]; // Remove duplicates
    }
    /**
     * Check if URL is a valid car detail page
     */
    isValidCarUrl(url) {
        return url.includes("/car/") && !url.includes("#") && !url.includes("?");
    }
    /**
     * Resolve relative URLs
     */
    resolveUrl(href) {
        if (href.startsWith("http")) {
            return href;
        }
        else if (href.startsWith("//")) {
            return `https:${href}`;
        }
        else if (href.startsWith("/")) {
            return `${config_1.siteConfig.baseUrl}${href}`;
        }
        else {
            return `${config_1.siteConfig.baseUrl}/${href}`;
        }
    }
    /**
     * Extract additional metadata
     */
    extractMetadata(data) {
        // Set production status based on availability indicators
        data.production_status = "current"; // Default assumption
        // Look for indicators of upcoming or discontinued models
        const pageText = this.$("body").text().toLowerCase();
        if (pageText.includes("coming soon") || pageText.includes("upcoming")) {
            data.production_status = "upcoming";
        }
        else if (pageText.includes("discontinued") ||
            pageText.includes("no longer")) {
            data.production_status = "discontinued";
        }
    }
    /**
     * Utility methods
     */
    getTextContent(selector) {
        const element = this.$(selector).first();
        return element.length > 0 ? (0, validation_1.cleanText)(element.text()) : null;
    }
    parseTitleString(title) {
        // Extract year from parentheses first (e.g., "Tesla Model Y RWD (2024-2025)")
        const yearInParentheses = title.match(/\((\d{4}(?:-\d{4})?)\)/);
        let extractedYear;
        let titleWithoutYear = title;
        if (yearInParentheses) {
            const yearStr = yearInParentheses[1];
            // Handle year ranges like "2024-2025" - take the first year
            const yearMatch = yearStr.match(/(\d{4})/);
            if (yearMatch) {
                extractedYear = parseInt(yearMatch[1]);
                titleWithoutYear = title.replace(/\s*\([^)]*\)/, "").trim();
            }
        }
        // Common patterns for EV titles (now without year in parentheses)
        const patterns = [
            /^(\w+)\s+(.+?)\s+(20\d{2})\s*(.*)$/, // "Tesla Model 3 2024 Long Range"
            /^(20\d{2})\s+(\w+)\s+(.+?)(?:\s+(.+))?$/, // "2024 Tesla Model 3 Long Range"
            /^(Tesla)\s+(Model\s+[3SXY]|Cybertruck|Roadster)\s*(.*)$/, // Tesla specific patterns
            /^(\w+)\s+(.+?)(?:\s+(.+))?$/, // "Tesla Model 3 Long Range"
        ];
        for (const pattern of patterns) {
            const match = titleWithoutYear.match(pattern);
            if (match) {
                if (pattern.source.startsWith("^(20\\d{2})")) {
                    // Year first pattern
                    return {
                        year: extractedYear || parseInt(match[1]),
                        make: match[2],
                        model: match[3],
                        trim: match[4],
                    };
                }
                else if (pattern.source.includes("(20\\d{2})")) {
                    // Year in middle pattern
                    return {
                        make: match[1],
                        model: match[2],
                        year: extractedYear || parseInt(match[3]),
                        trim: match[4],
                    };
                }
                else if (pattern.source.includes("Tesla.*Model")) {
                    // Tesla specific pattern
                    return {
                        make: match[1], // Tesla
                        model: match[2], // Model 3, Model S, etc.
                        year: extractedYear,
                        trim: match[3],
                    };
                }
                else {
                    // No year pattern
                    return {
                        make: match[1],
                        model: match[2],
                        year: extractedYear,
                        trim: match[3],
                    };
                }
            }
        }
        // Fallback: try to extract basic info even if patterns don't match
        const words = titleWithoutYear.split(/\s+/);
        if (words.length >= 2) {
            return {
                make: words[0],
                model: words.slice(1, 3).join(" "), // Take up to 2 words for model
                year: extractedYear,
                trim: words.length > 3 ? words.slice(3).join(" ") : undefined,
            };
        }
        return { year: extractedYear };
    }
    parseDrivetrain(text) {
        const lower = text.toLowerCase();
        if (lower.includes("awd") || lower.includes("all-wheel"))
            return "AWD";
        if (lower.includes("4wd") || lower.includes("four-wheel"))
            return "4WD";
        if (lower.includes("fwd") || lower.includes("front-wheel"))
            return "FWD";
        if (lower.includes("rwd") || lower.includes("rear-wheel"))
            return "RWD";
        // If we can't determine the drivetrain type, return undefined
        return undefined;
    }
    resolveImageUrl(src) {
        if (src.startsWith("http")) {
            return src;
        }
        else if (src.startsWith("//")) {
            return `https:${src}`;
        }
        else if (src.startsWith("/")) {
            return `${config_1.siteConfig.baseUrl}${src}`;
        }
        else {
            return `${config_1.siteConfig.baseUrl}/${src}`;
        }
    }
}
exports.EVDataParser = EVDataParser;
//# sourceMappingURL=parser.js.map