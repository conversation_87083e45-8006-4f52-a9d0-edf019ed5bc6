{"version": 3, "file": "parser.d.ts", "sourceRoot": "", "sources": ["../../src/scraper/parser.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,UAAU,CAAC;AAUnC;;GAEG;AACH,qBAAa,YAAY;IACvB,OAAO,CAAC,CAAC,CAAqB;gBAElB,IAAI,EAAE,MAAM;IAIxB;;OAEG;IACH,cAAc,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAiD7C;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAuDxB;;OAEG;IACH,OAAO,CAAC,qBAAqB;IA2B7B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAyE7B;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAmC1B;;OAEG;IACH,OAAO,CAAC,cAAc;IAUtB;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAqB1B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAWzB;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAK5B;;OAEG;IACH,OAAO,CAAC,aAAa;IAsBrB;;OAEG;IACH,eAAe,IAAI,MAAM,EAAE;IAgB3B;;OAEG;IACH,OAAO,CAAC,aAAa;IAIrB;;OAEG;IACH,OAAO,CAAC,UAAU;IAYlB;;OAEG;IACH,OAAO,CAAC,eAAe;IAgBvB;;OAEG;IACH,OAAO,CAAC,cAAc;IAKtB,OAAO,CAAC,gBAAgB;IA8CxB,OAAO,CAAC,eAAe;IAavB,OAAO,CAAC,eAAe;CAWxB"}