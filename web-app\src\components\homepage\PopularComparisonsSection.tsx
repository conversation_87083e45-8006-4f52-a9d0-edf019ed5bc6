'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  ArrowRight, 
  Zap, 
  MapPin, 
  DollarSign,
  Users,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

interface PopularComparison {
  id: string
  title: string
  models: {
    id: string
    make: string
    model: string
    year: number
    price_msrp: number | null
    range_epa_miles: number | null
    images: string[] | null
  }[]
  comparisonCount: number
}

interface PopularComparisonsSectionProps {
  comparisons: PopularComparison[]
  loading?: boolean
}

export function PopularComparisonsSection({ comparisons, loading }: PopularComparisonsSectionProps) {
  if (loading) {
    return (
      <section className="bg-gray-50 py-16 dark:bg-gray-900">
        <div className="container">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="mx-auto h-8 w-64 bg-gray-300 rounded mb-4"></div>
              <div className="mx-auto h-4 w-96 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  if (!comparisons || comparisons.length === 0) {
    return null
  }

  const formatPrice = (price: number | null) => {
    if (!price) return 'TBA'
    return `$${(price / 100).toLocaleString()}`
  }

  const getMainImage = (images: string[] | null) => {
    if (!images || images.length === 0) {
      return 'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=400&h=300&fit=crop&crop=center'
    }
    return images[0]
  }

  // Mock additional popular comparisons for demonstration
  const mockComparisons: PopularComparison[] = [
    {
      id: 'tesla-vs-bmw',
      title: 'Tesla Model 3 vs BMW i4',
      models: [
        {
          id: '1',
          make: 'Tesla',
          model: 'Model 3',
          year: 2024,
          price_msrp: 4099900, // $40,999
          range_epa_miles: 358,
          images: ['https://images.unsplash.com/photo-1617788138017-80ad40651399?w=400&h=300&fit=crop']
        },
        {
          id: '2',
          make: 'BMW',
          model: 'i4',
          year: 2024,
          price_msrp: 5699500, // $56,995
          range_epa_miles: 270,
          images: ['https://images.unsplash.com/photo-1555215695-3004980ad54e?w=400&h=300&fit=crop']
        }
      ],
      comparisonCount: 1247
    },
    {
      id: 'family-suv-showdown',
      title: 'Family SUV Showdown',
      models: [
        {
          id: '3',
          make: 'Ford',
          model: 'Mustang Mach-E',
          year: 2024,
          price_msrp: 4899900, // $48,999
          range_epa_miles: 312,
          images: ['https://images.unsplash.com/photo-1609521263047-f8f205293f24?w=400&h=300&fit=crop']
        },
        {
          id: '4',
          make: 'Hyundai',
          model: 'IONIQ 5',
          year: 2024,
          price_msrp: 4149500, // $41,495
          range_epa_miles: 303,
          images: ['https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?w=400&h=300&fit=crop']
        }
      ],
      comparisonCount: 892
    },
    {
      id: 'budget-ev-battle',
      title: 'Budget EV Battle',
      models: [
        {
          id: '5',
          make: 'Nissan',
          model: 'Leaf',
          year: 2024,
          price_msrp: 2899900, // $28,999
          range_epa_miles: 212,
          images: ['https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=400&h=300&fit=crop']
        },
        {
          id: '6',
          make: 'Chevrolet',
          model: 'Bolt EV',
          year: 2024,
          price_msrp: 3199500, // $31,995
          range_epa_miles: 259,
          images: ['https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=400&h=300&fit=crop']
        }
      ],
      comparisonCount: 634
    }
  ]

  const displayComparisons = comparisons.length > 0 ? comparisons : mockComparisons

  return (
    <section className="bg-gray-50 py-16 dark:bg-gray-900">
      <div className="container">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <Badge variant="secondary" className="mb-4">
            <BarChart3 className="mr-1 h-3 w-3" />
            Popular Comparisons
          </Badge>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl">
            See How EVs Stack Up
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            Discover the most popular EV comparisons and see detailed side-by-side analysis
          </p>
        </div>

        {/* Comparisons Grid */}
        <div className="grid gap-8 lg:grid-cols-3">
          {displayComparisons.slice(0, 3).map((comparison) => (
            <Card key={comparison.id} className="group overflow-hidden border-0 shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{comparison.title}</CardTitle>
                  <Badge variant="outline" className="text-xs">
                    <Users className="mr-1 h-3 w-3" />
                    {comparison.comparisonCount.toLocaleString()}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Model Comparison */}
                <div className="space-y-3">
                  {comparison.models.slice(0, 2).map((model, index) => (
                    <div key={model.id} className="flex items-center gap-3 rounded-lg border p-3">
                      <div className="relative h-16 w-20 overflow-hidden rounded">
                        <Image
                          src={getMainImage(model.images)}
                          alt={`${model.make} ${model.model}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-sm truncate">
                          {model.make} {model.model}
                        </h4>
                        <div className="flex items-center gap-4 text-xs text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            {formatPrice(model.price_msrp)}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {model.range_epa_miles ? `${model.range_epa_miles}mi` : 'TBA'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Comparison Stats */}
                <div className="rounded-lg bg-electric-50 p-3 dark:bg-electric-950">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-electric-800 dark:text-electric-200">
                      Price Difference
                    </span>
                    <span className="font-semibold text-electric-900 dark:text-electric-100">
                      {comparison.models.length >= 2 && comparison.models[0].price_msrp && comparison.models[1].price_msrp
                        ? `$${Math.abs((comparison.models[0].price_msrp - comparison.models[1].price_msrp) / 100).toLocaleString()}`
                        : 'Varies'
                      }
                    </span>
                  </div>
                </div>

                {/* CTA */}
                <Button className="w-full group" variant="outline">
                  View Full Comparison
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Comparisons CTA */}
        <div className="mt-12 text-center">
          <Link href="/compare">
            <Button size="lg" className="bg-electric-600 hover:bg-electric-700 group">
              <BarChart3 className="mr-2 h-4 w-4" />
              Start Your Own Comparison
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
