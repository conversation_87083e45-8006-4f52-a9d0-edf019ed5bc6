"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EVScraper = void 0;
const p_limit_1 = __importDefault(require("p-limit"));
const p_retry_1 = __importDefault(require("p-retry"));
const browser_1 = require("./browser");
const parser_1 = require("./parser");
const output_1 = require("../utils/output");
const validation_1 = require("../utils/validation");
const logger_1 = require("../utils/logger");
const config_1 = require("../utils/config");
const database_1 = require("../database");
/**
 * Main EV scraper class
 */
class EVScraper {
    config;
    browserManager;
    outputManager;
    database;
    progressLogger;
    scrapedData = [];
    errors = [];
    constructor(config) {
        this.config = config;
        this.browserManager = new browser_1.BrowserManager(config);
        this.outputManager = new output_1.OutputManager(config.output_directory);
        this.database = new database_1.EVDatabase();
    }
    /**
     * Main scraping method
     */
    async scrape() {
        const startTime = Date.now();
        try {
            logger_1.logger.info("Starting EV scraping session", {
                config: this.config,
                timestamp: new Date().toISOString(),
            });
            // Initialize browser
            await this.browserManager.initialize();
            // Get all car URLs to scrape
            const carUrls = await this.discoverCarUrls();
            if (carUrls.length === 0) {
                throw new Error("No car URLs found to scrape");
            }
            // Initialize progress tracking
            this.progressLogger = new logger_1.ProgressLogger(carUrls.length);
            // Scrape all cars with concurrency control
            await this.scrapeAllCars(carUrls);
            // Save results
            const outputFiles = await this.saveResults();
            // Generate final result
            const result = {
                success: true,
                total_vehicles: carUrls.length,
                successful_scrapes: this.scrapedData.length,
                failed_scrapes: this.errors.length,
                errors: this.errors,
                execution_time_ms: Date.now() - startTime,
                output_files: outputFiles,
            };
            this.progressLogger.logCompletion();
            logger_1.logger.info("Scraping session completed successfully", result);
            return result;
        }
        catch (error) {
            const result = {
                success: false,
                total_vehicles: 0,
                successful_scrapes: this.scrapedData.length,
                failed_scrapes: this.errors.length,
                errors: this.errors,
                execution_time_ms: Date.now() - startTime,
                output_files: [],
            };
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Scraping session failed", {
                error: errorMessage,
                result,
            });
            return result;
        }
        finally {
            await this.browserManager.close();
            this.database.close();
        }
    }
    /**
     * Discover all car URLs to scrape
     */
    async discoverCarUrls() {
        logger_1.logger.info("Discovering car URLs");
        const page = await this.browserManager.createPage();
        const allUrls = [];
        try {
            // Start with the main car listing page
            const success = await this.browserManager.navigateToUrl(page, config_1.siteConfig.urls.carList);
            if (!success) {
                throw new Error("Failed to load car listing page");
            }
            // Extract car links from current page
            const html = await this.browserManager.extractPageContent(page);
            const parser = new parser_1.EVDataParser(html);
            const carLinks = parser.extractCarLinks();
            allUrls.push(...carLinks);
            logger_1.logger.info("Found car links on main page", { count: carLinks.length });
            // Look for pagination and scrape additional pages
            const additionalUrls = await this.scrapePaginatedPages(page);
            allUrls.push(...additionalUrls);
            // Remove duplicates and apply filters
            const uniqueUrls = [...new Set(allUrls)];
            const filteredUrls = this.applyUrlFilters(uniqueUrls);
            // Filter out already scraped URLs (unless force re-scraping is enabled)
            const unscrapedUrls = this.config.force_rescrape
                ? filteredUrls
                : filteredUrls.filter((url) => !this.database.isUrlScraped(url));
            logger_1.logger.info("Car URL discovery completed", {
                totalFound: uniqueUrls.length,
                afterFiltering: filteredUrls.length,
                alreadyScraped: filteredUrls.length - unscrapedUrls.length,
                toScrape: unscrapedUrls.length,
            });
            return unscrapedUrls;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to discover car URLs", { error: errorMessage });
            throw error;
        }
        finally {
            await page.close();
        }
    }
    /**
     * Scrape additional pages from pagination
     */
    async scrapePaginatedPages(page) {
        const allUrls = [];
        let currentPage = 1;
        const maxPages = this.config.max_pages || 10;
        while (currentPage < maxPages) {
            try {
                // Look for next page link
                const nextPageExists = await this.browserManager.elementExists(page, config_1.siteConfig.selectors.nextPage);
                if (!nextPageExists) {
                    logger_1.logger.info("No more pages found", { currentPage });
                    break;
                }
                // Click next page
                await page.click(config_1.siteConfig.selectors.nextPage);
                await page
                    .waitForLoadState("networkidle", { timeout: 10000 })
                    .catch(() => { });
                currentPage++;
                logger_1.logger.debug("Navigated to page", { currentPage });
                // Extract links from this page
                const html = await this.browserManager.extractPageContent(page);
                const parser = new parser_1.EVDataParser(html);
                const carLinks = parser.extractCarLinks();
                allUrls.push(...carLinks);
                logger_1.logger.debug("Found car links on page", {
                    page: currentPage,
                    count: carLinks.length,
                });
                // Rate limiting between pages
                await this.delay(this.config.delay_between_requests);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                logger_1.logger.warn("Failed to scrape page", {
                    page: currentPage,
                    error: errorMessage,
                });
                break;
            }
        }
        return allUrls;
    }
    /**
     * Apply filters to discovered URLs
     */
    applyUrlFilters(urls) {
        let filtered = urls;
        // Apply make filter if specified
        if (this.config.specific_makes && this.config.specific_makes.length > 0) {
            filtered = filtered.filter((url) => {
                const urlLower = url.toLowerCase();
                return this.config.specific_makes.some((make) => urlLower.includes(make.toLowerCase()));
            });
        }
        // Apply max pages limit
        if (this.config.max_pages) {
            filtered = filtered.slice(0, this.config.max_pages);
        }
        return filtered;
    }
    /**
     * Scrape all car URLs with concurrency control
     */
    async scrapeAllCars(urls) {
        const limit = (0, p_limit_1.default)(this.config.max_concurrent_requests);
        const scrapingPromises = urls.map((url) => limit(() => this.scrapeCarWithRetry(url)));
        await Promise.allSettled(scrapingPromises);
    }
    /**
     * Scrape a single car with retry logic
     */
    async scrapeCarWithRetry(url) {
        try {
            await (0, p_retry_1.default)(() => this.scrapeSingleCar(url), {
                retries: this.config.max_retries,
                minTimeout: this.config.retry_delay,
                onFailedAttempt: (error) => {
                    logger_1.logger.warn("Retry attempt failed", {
                        url,
                        attempt: error.attemptNumber,
                        retriesLeft: error.retriesLeft,
                        error: error instanceof Error ? error.message : String(error),
                    });
                },
            });
        }
        catch (error) {
            this.handleScrapingError(url, error, this.config.max_retries);
        }
    }
    /**
     * Scrape a single car page
     */
    async scrapeSingleCar(url) {
        const page = await this.browserManager.createPage();
        try {
            // Navigate to car page
            const success = await this.browserManager.navigateToUrl(page, url);
            if (!success) {
                throw new Error("Failed to load car page");
            }
            // Extract page content
            const html = await this.browserManager.extractPageContent(page);
            if (!html || html.length < 1000) {
                throw new Error("Page content too short or empty");
            }
            // Parse EV data
            const parser = new parser_1.EVDataParser(html);
            const evData = parser.extractEVModel(url);
            // Validate data
            const validation = (0, validation_1.validateEVModel)(evData);
            (0, validation_1.logValidationResults)(url, validation);
            if (validation.isValid && validation.cleanedData) {
                // Save to database
                const vehicleId = this.database.upsertVehicle(validation.cleanedData);
                this.database.recordScrapedUrl(url, true, vehicleId);
                // Keep in memory for legacy output files
                this.scrapedData.push(validation.cleanedData);
                this.progressLogger?.logProgress(url, true, {
                    make: validation.cleanedData.make,
                    model: validation.cleanedData.model,
                });
            }
            else {
                // Record failed scrape
                this.database.recordScrapedUrl(url, false, undefined, validation.errors.join(", "));
                throw new Error(`Data validation failed: ${validation.errors.join(", ")}`);
            }
        }
        catch (error) {
            throw error; // Re-throw for retry logic
        }
        finally {
            await page.close();
        }
    }
    /**
     * Handle scraping errors
     */
    handleScrapingError(url, error, retryCount) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const scrapingError = {
            url,
            error_message: errorMessage,
            timestamp: new Date().toISOString(),
            retry_count: retryCount,
        };
        // Record failed scrape in database
        this.database.recordScrapedUrl(url, false, undefined, errorMessage);
        this.errors.push(scrapingError);
        this.progressLogger?.logError(url, error, retryCount);
    }
    /**
     * Save all results to files
     */
    async saveResults() {
        const outputFiles = [];
        try {
            // Save scraped data
            if (this.scrapedData.length > 0) {
                if (this.config.save_json) {
                    const jsonFile = await this.outputManager.saveAsJSON(this.scrapedData);
                    outputFiles.push(jsonFile);
                }
                if (this.config.save_csv) {
                    const csvFile = await this.outputManager.saveAsCSV(this.scrapedData);
                    outputFiles.push(csvFile);
                }
                if (this.config.save_sql) {
                    const sqlFile = await this.outputManager.saveAsSQL(this.scrapedData);
                    outputFiles.push(sqlFile);
                }
            }
            // Save errors if any
            if (this.errors.length > 0) {
                const errorsFile = await this.outputManager.saveErrors(this.errors);
                outputFiles.push(errorsFile);
            }
            return outputFiles;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to save results", { error: errorMessage });
            throw error;
        }
    }
    /**
     * Utility delay function
     */
    async delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
}
exports.EVScraper = EVScraper;
//# sourceMappingURL=EVScraper.js.map