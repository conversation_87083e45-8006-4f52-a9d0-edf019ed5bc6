This page contains links to some examples of existing `binding.gyp` files that other node modules are using. Take a look at them for inspiration.

To add to this page, just add the link to the project's `binding.gyp` file below:

 * [ons](https://github.com/XadillaX/aliyun-ons/blob/master/binding.gyp)
 * [thmclrx](https://github.com/XadillaX/thmclrx/blob/master/binding.gyp)
 * [libxmljs](https://github.com/polotek/libxmljs/blob/master/binding.gyp)
 * [node-buffertools](https://github.com/bnoordhuis/node-buffertools/blob/master/binding.gyp)
 * [node-canvas](https://github.com/LearnBoost/node-canvas/blob/master/binding.gyp)
 * [node-ffi](https://github.com/rbranson/node-ffi/blob/master/binding.gyp) + [libffi](https://github.com/rbranson/node-ffi/blob/master/deps/libffi/libffi.gyp)
 * [node-time](https://github.com/TooTallNate/node-time/blob/master/binding.gyp)
 * [node-sass](https://github.com/sass/node-sass/blob/master/binding.gyp) + [libsass](https://github.com/sass/node-sass/blob/master/src/libsass.gyp)
 * [node-serialport](https://github.com/voodootikigod/node-serialport/blob/master/binding.gyp)
 * [node-weak](https://github.com/TooTallNate/node-weak/blob/master/binding.gyp)
 * [pty.js](https://github.com/chjj/pty.js/blob/master/binding.gyp)
 * [ref](https://github.com/TooTallNate/ref/blob/master/binding.gyp)
 * [appjs](https://github.com/milani/appjs/blob/master/binding.gyp)
 * [nwm](https://github.com/mixu/nwm/blob/master/binding.gyp)
 * [bcrypt](https://github.com/ncb000gt/node.bcrypt.js/blob/master/binding.gyp)
 * [nk-mysql](https://github.com/mmod/nodamysql/blob/master/binding.gyp)
 * [nk-xrm-installer](https://github.com/mmod/nk-xrm-installer/blob/master/binding.gyp) + [includable.gypi](https://github.com/mmod/nk-xrm-installer/blob/master/includable.gypi) + [unpack.py](https://github.com/mmod/nk-xrm-installer/blob/master/unpack.py) + [disburse.py](https://github.com/mmod/nk-xrm-installer/blob/master/disburse.py)   
   <sub>.py files above provide complete reference for examples of fetching source via http, extracting, and moving files.</sub>
 * [node-memwatch](https://github.com/lloyd/node-memwatch/blob/master/binding.gyp)
 * [node-ip2location](https://github.com/bolgovr/node-ip2location/blob/master/binding.gyp)
 * [node-midi](https://github.com/justinlatimer/node-midi/blob/master/binding.gyp)
 * [node-sqlite3](https://github.com/developmentseed/node-sqlite3/blob/master/binding.gyp) + [libsqlite3](https://github.com/developmentseed/node-sqlite3/blob/master/deps/sqlite3.gyp)
 * [node-zipfile](https://github.com/mapbox/node-zipfile/blob/master/binding.gyp)
 * [node-mapnik](https://github.com/mapnik/node-mapnik/blob/master/binding.gyp)
 * [node-inotify](https://github.com/c4milo/node-inotify/blob/master/binding.gyp)
 * [v8-profiler](https://github.com/c4milo/v8-profiler/blob/master/binding.gyp)
 * [airtunes](https://github.com/radioline/node_airtunes/blob/master/binding.gyp)
 * [node-fann](https://github.com/c4milo/node-fann/blob/master/binding.gyp)
 * [node-talib](https://github.com/oransel/node-talib/blob/master/binding.gyp)
 * [node-leveldown](https://github.com/rvagg/node-leveldown/blob/master/binding.gyp) + [leveldb.gyp](https://github.com/rvagg/node-leveldown/blob/master/deps/leveldb/leveldb.gyp) + [snappy.gyp](https://github.com/rvagg/node-leveldown/blob/master/deps/snappy/snappy.gyp)
 * [node-expat](https://github.com/astro/node-expat/blob/master/binding.gyp) + [libexpat](https://github.com/astro/node-expat/blob/master/deps/libexpat/libexpat.gyp)
 * [node-openvg-canvas](https://github.com/luismreis/node-openvg-canvas/blob/master/binding.gyp) + [node-openvg](https://github.com/luismreis/node-openvg/blob/master/binding.gyp)
 * [node-cryptopp](https://github.com/BatikhSouri/node-cryptopp/blob/master/binding.gyp)
 * [topcube](https://github.com/creationix/topcube/blob/master/binding.gyp)
 * [node-osmium](https://github.com/osmcode/node-osmium/blob/master/binding.gyp)
 * [node-osrm](https://github.com/DennisOSRM/node-osrm)
 * [node-oracle](https://github.com/joeferner/node-oracle/blob/master/binding.gyp)
 * [node-process-list](https://github.com/ReklatsMasters/node-process-list/blob/master/binding.gyp)
 * [node-nanomsg](https://github.com/nickdesaulniers/node-nanomsg/blob/master/binding.gyp)
 * [Ghostscript4JS](https://github.com/NickNaso/ghostscript4js/blob/master/binding.gyp)
 * [nodecv](https://github.com/xudafeng/nodecv/blob/master/binding.gyp)
 * [magick-cli](https://github.com/NickNaso/magick-cli/blob/master/binding.gyp)
 * [sharp](https://github.com/lovell/sharp/blob/master/binding.gyp)
 * [krb5](https://github.com/adaltas/node-krb5/blob/master/binding.gyp)