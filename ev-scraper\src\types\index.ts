/**
 * Electric Vehicle Data Types
 * Comprehensive type definitions for scraped EV data
 */

export interface EVModel {
  // Basic Information
  id?: string;
  make: string;
  model: string;
  year: number;
  trim?: string;
  variant?: string;

  // Pricing
  price_msrp?: number; // in cents (e.g., 4099900 for $40,999)
  price_base?: number;
  price_including_incentives?: number;
  currency?: string;

  // Battery & Range
  battery_capacity_kwh: number;
  battery_usable_kwh?: number;
  range_epa_miles?: number;
  range_wltp_km?: number;
  range_real_world_miles?: number;

  // Charging
  charging_speed_ac_kw?: number;
  charging_speed_dc_kw?: number;
  charging_time_10_80_minutes?: number;
  charging_time_0_100_minutes?: number;
  charging_port_type?: string;

  // Performance
  acceleration_0_60_mph?: number;
  acceleration_0_100_kmh?: number;
  top_speed_mph?: number;
  top_speed_kmh?: number;

  // Efficiency
  efficiency_mpge?: number;
  efficiency_kwh_100km?: number;
  efficiency_miles_kwh?: number;

  // Physical Specifications
  body_type?: string;
  seating_capacity?: number;
  doors?: number;
  cargo_space_cu_ft?: number;
  cargo_space_liters?: number;

  // Dimensions
  length_mm?: number;
  width_mm?: number;
  height_mm?: number;
  wheelbase_mm?: number;
  weight_kg?: number;

  // Drivetrain
  drivetrain?: "FWD" | "RWD" | "AWD" | "4WD";
  motor_power_kw?: number;
  motor_power_hp?: number;
  motor_torque_nm?: number;

  // Additional Info
  production_status?: "current" | "upcoming" | "discontinued";
  availability_region?: string[];
  manufacturer_id?: string;

  // Media
  images?: string[];

  // Metadata
  source_url?: string;
  scraped_at?: string;
  last_updated?: string;

  // Flags for categorization
  is_featured?: boolean;
  best_value?: boolean;
  editor_choice?: boolean;
}

export interface EVManufacturer {
  id: string;
  name: string;
  country?: string;
  logo_url?: string;
  website_url?: string;
  founded_year?: number;
  description?: string;
}

export interface ScrapingConfig {
  // Rate limiting
  delay_between_requests: number;
  max_concurrent_requests: number;

  // Retry logic
  max_retries: number;
  retry_delay: number;

  // Browser settings
  headless: boolean;
  user_agent?: string;
  viewport_width: number;
  viewport_height: number;

  // Output settings
  output_directory: string;
  save_json: boolean;
  save_csv: boolean;
  save_sql: boolean;

  // Scraping scope
  max_pages?: number;
  specific_makes?: string[];
  year_range?: {
    start: number;
    end: number;
  };

  // Database options
  force_rescrape?: boolean;
}

export interface ScrapingResult {
  success: boolean;
  total_vehicles: number;
  successful_scrapes: number;
  failed_scrapes: number;
  errors: ScrapingError[];
  execution_time_ms: number;
  output_files: string[];
}

export interface ScrapingError {
  url: string;
  error_message: string;
  timestamp: string;
  retry_count: number;
}

export interface PageData {
  url: string;
  html: string;
  status_code: number;
  scraped_at: string;
}

// Database schema compatibility types
export interface DatabaseEVModel {
  id: string;
  make: string;
  model: string;
  year: number;
  trim?: string;
  price_msrp?: number;
  battery_capacity_kwh: number;
  range_epa_miles?: number;
  charging_speed_dc_kw?: number;
  acceleration_0_60_mph?: number;
  top_speed_mph?: number;
  efficiency_mpge?: number;
  body_type?: string;
  seating_capacity?: number;
  cargo_space_cu_ft?: number;
  drivetrain?: string;
  production_status?: string;
  is_featured?: boolean;
  best_value?: boolean;
  editor_choice?: boolean;
  images?: string[];
  manufacturer_id?: string;
  created_at: string;
  updated_at: string;
}

// Utility types
export type ScrapingStatus = "pending" | "in_progress" | "completed" | "failed";

export interface ProgressTracker {
  total_urls: number;
  processed_urls: number;
  successful_scrapes: number;
  failed_scrapes: number;
  current_url?: string;
  status: ScrapingStatus;
  start_time: Date;
  estimated_completion?: Date;
}
