import { ScrapingConfig, ScrapingResult } from "../types";
/**
 * Main EV scraper class
 */
export declare class EVScraper {
    private config;
    private browserManager;
    private outputManager;
    private database;
    private progressLogger?;
    private scrapedData;
    private errors;
    constructor(config: ScrapingConfig);
    /**
     * Main scraping method
     */
    scrape(): Promise<ScrapingResult>;
    /**
     * Discover all car URLs to scrape
     */
    private discoverCarUrls;
    /**
     * Scrape additional pages from pagination
     */
    private scrapePaginatedPages;
    /**
     * Apply filters to discovered URLs
     */
    private applyUrlFilters;
    /**
     * Scrape all car URLs with concurrency control
     */
    private scrapeAllCars;
    /**
     * Scrape a single car with retry logic
     */
    private scrapeCarWithRetry;
    /**
     * Scrape a single car page
     */
    private scrapeSingleCar;
    /**
     * Handle scraping errors
     */
    private handleScrapingError;
    /**
     * Save all results to files
     */
    private saveResults;
    /**
     * Utility delay function
     */
    private delay;
}
//# sourceMappingURL=EVScraper.d.ts.map