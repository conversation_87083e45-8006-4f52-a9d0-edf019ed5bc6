{"name": "ev-scraper", "version": "1.0.0", "description": "Web scraper for extracting electric vehicle data from ev-database.org", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "scrape": "npm run build && npm start", "scrape:dev": "ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "keywords": ["web-scraping", "electric-vehicles", "ev-database", "typescript", "puppeteer"], "author": "EV App Team", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "cheerio": "^1.0.0-rc.12", "csv-writer": "^1.6.0", "fs-extra": "^11.1.1", "p-limit": "^4.0.0", "p-retry": "^6.2.0", "winston": "^3.11.0", "yargs": "^17.7.2", "dotenv": "^16.3.1", "robots-parser": "^3.0.1", "node-fetch": "^2.7.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/fs-extra": "^11.0.4", "@types/yargs": "^17.0.32", "@types/node-fetch": "^2.6.9", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}}