import * as cheerio from "cheerio";
import { EVModel } from "../types";
import { siteConfig } from "../utils/config";
import {
  extractNumericValue,
  parsePrice,
  parseRange,
  cleanText,
} from "../utils/validation";
import { logger } from "../utils/logger";

/**
 * HTML parsing utilities for extracting EV data
 */
export class EVDataParser {
  private $: cheerio.CheerioAPI;

  constructor(html: string) {
    this.$ = cheerio.load(html);
  }

  /**
   * Extract EV model data from a detail page
   */
  extractEVModel(url: string): Partial<EVModel> {
    const data: Partial<EVModel> = {
      source_url: url,
      scraped_at: new Date().toISOString(),
    };

    try {
      // Extract basic information
      this.extractBasicInfo(data);

      // Extract specifications
      this.extractSpecifications(data);

      // Extract pricing
      this.extractPricing(data);

      // Extract performance data
      this.extractPerformance(data);

      // Extract efficiency data
      this.extractEfficiency(data);

      // Extract physical specifications
      this.extractPhysicalSpecs(data);

      // Extract images
      this.extractImages(data);

      // Extract additional metadata
      this.extractMetadata(data);

      logger.debug("Extracted EV data", {
        url,
        make: data.make,
        model: data.model,
        fieldsExtracted: Object.keys(data).length,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to extract EV data", {
        url,
        error: errorMessage,
      });
    }

    return data;
  }

  /**
   * Extract basic vehicle information
   */
  private extractBasicInfo(data: Partial<EVModel>): void {
    // Extract title and parse make/model
    const title = this.getTextContent(siteConfig.selectors.carTitle);
    if (title) {
      const titleParts = this.parseTitleString(title);
      data.make = titleParts.make;
      data.model = titleParts.model;
      data.year = titleParts.year;
      data.trim = titleParts.trim;
    }

    // Try alternative selectors for make/model
    if (!data.make || !data.model) {
      data.make =
        data.make ||
        this.getTextContent(".make, .brand, .manufacturer") ||
        undefined;
      data.model =
        data.model ||
        this.getTextContent(".model, .vehicle-model") ||
        undefined;
    }

    // Extract year if not found in title
    if (!data.year) {
      const yearText = this.getTextContent(".year, .model-year");
      if (yearText) {
        const yearMatch = yearText.match(/20\d{2}/);
        if (yearMatch) {
          data.year = parseInt(yearMatch[0]);
        }
      }
    }

    // Extract body type
    data.body_type = this.getTextContent(
      siteConfig.selectors.bodyType
    )?.toLowerCase();

    // Extract drivetrain
    const drivetrainText = this.getTextContent(siteConfig.selectors.drivetrain);
    if (drivetrainText) {
      data.drivetrain = this.parseDrivetrain(drivetrainText);
    }

    // Extract seating capacity
    const seatingText = this.getTextContent(siteConfig.selectors.seating);
    if (seatingText) {
      const seatingMatch = seatingText.match(/(\d+)/);
      if (seatingMatch) {
        data.seating_capacity = parseInt(seatingMatch[1]);
      }
    }
  }

  /**
   * Extract technical specifications from spec tables
   */
  private extractSpecifications(data: Partial<EVModel>): void {
    // Look for specification tables
    const specTables = this.$(siteConfig.selectors.specTable);

    specTables.each((_, table) => {
      const $table = this.$(table);
      const rows = $table.find(siteConfig.selectors.specRows);

      rows.each((_, row) => {
        const $row = this.$(row);
        const label = $row
          .find(siteConfig.selectors.specLabel)
          .text()
          .trim()
          .toLowerCase();
        const value = $row.find(siteConfig.selectors.specValue).text().trim();

        if (!label || !value) return;

        this.parseSpecificationRow(label, value, data);
      });
    });

    // Try direct selectors for common specs
    this.extractDirectSpecs(data);
  }

  /**
   * Parse individual specification rows
   */
  private parseSpecificationRow(
    label: string,
    value: string,
    data: Partial<EVModel>
  ): void {
    // Battery capacity
    if (label.includes("battery") && label.includes("capacity")) {
      const capacity = extractNumericValue(value, siteConfig.patterns.battery);
      if (capacity) data.battery_capacity_kwh = capacity;
    }

    // Range
    if (label.includes("range") || label.includes("epa")) {
      const range = parseRange(value);
      if (range) data.range_epa_miles = range;
    }

    // Charging speed
    if (
      label.includes("charging") &&
      (label.includes("dc") || label.includes("fast"))
    ) {
      const charging = extractNumericValue(value, siteConfig.patterns.charging);
      if (charging) data.charging_speed_dc_kw = charging;
    }

    if (label.includes("charging") && label.includes("ac")) {
      const charging = extractNumericValue(value, siteConfig.patterns.charging);
      if (charging) data.charging_speed_ac_kw = charging;
    }

    // Acceleration
    if (label.includes("0-60") || label.includes("acceleration")) {
      const accel = extractNumericValue(
        value,
        siteConfig.patterns.acceleration
      );
      if (accel) data.acceleration_0_60_mph = accel;
    }

    // Top speed
    if (label.includes("top speed") || label.includes("max speed")) {
      const speed = extractNumericValue(value, siteConfig.patterns.speed);
      if (speed) data.top_speed_mph = speed;
    }

    // Efficiency
    if (label.includes("mpge") || label.includes("efficiency")) {
      const efficiency = extractNumericValue(
        value,
        siteConfig.patterns.efficiency
      );
      if (efficiency) data.efficiency_mpge = efficiency;
    }

    // Power
    if (label.includes("power") || label.includes("motor")) {
      const powerMatch = value.match(siteConfig.patterns.power);
      if (powerMatch) {
        const power = parseFloat(powerMatch[1]);
        const unit = powerMatch[2].toLowerCase();

        if (unit === "hp") {
          data.motor_power_hp = power;
          data.motor_power_kw = Math.round(power * 0.746); // Convert HP to kW
        } else if (unit === "kw") {
          data.motor_power_kw = power;
          data.motor_power_hp = Math.round(power * 1.341); // Convert kW to HP
        }
      }
    }
  }

  /**
   * Extract specifications using direct selectors
   */
  private extractDirectSpecs(data: Partial<EVModel>): void {
    // Battery
    if (!data.battery_capacity_kwh) {
      const batteryText = this.getTextContent(siteConfig.selectors.battery);
      if (batteryText) {
        const capacity = extractNumericValue(
          batteryText,
          siteConfig.patterns.battery
        );
        if (capacity) data.battery_capacity_kwh = capacity;
      }
    }

    // Range
    if (!data.range_epa_miles) {
      const rangeText = this.getTextContent(siteConfig.selectors.range);
      if (rangeText) {
        const range = parseRange(rangeText);
        if (range) data.range_epa_miles = range;
      }
    }

    // Charging
    if (!data.charging_speed_dc_kw) {
      const chargingText = this.getTextContent(siteConfig.selectors.charging);
      if (chargingText) {
        const charging = extractNumericValue(
          chargingText,
          siteConfig.patterns.charging
        );
        if (charging) data.charging_speed_dc_kw = charging;
      }
    }
  }

  /**
   * Extract pricing information
   */
  private extractPricing(data: Partial<EVModel>): void {
    const priceText = this.getTextContent(siteConfig.selectors.price);
    if (priceText) {
      const price = parsePrice(priceText);
      if (price) {
        data.price_msrp = price;
      }
    }
  }

  /**
   * Extract performance data
   */
  private extractPerformance(data: Partial<EVModel>): void {
    const performanceText = this.getTextContent(
      siteConfig.selectors.performance
    );
    if (performanceText) {
      // Try to extract acceleration
      const accel = extractNumericValue(
        performanceText,
        siteConfig.patterns.acceleration
      );
      if (accel) data.acceleration_0_60_mph = accel;

      // Try to extract top speed
      const speed = extractNumericValue(
        performanceText,
        siteConfig.patterns.speed
      );
      if (speed) data.top_speed_mph = speed;
    }
  }

  /**
   * Extract efficiency data
   */
  private extractEfficiency(data: Partial<EVModel>): void {
    const efficiencyText = this.getTextContent(siteConfig.selectors.efficiency);
    if (efficiencyText) {
      const efficiency = extractNumericValue(
        efficiencyText,
        siteConfig.patterns.efficiency
      );
      if (efficiency) data.efficiency_mpge = efficiency;
    }
  }

  /**
   * Extract physical specifications
   */
  private extractPhysicalSpecs(data: Partial<EVModel>): void {
    // This would be expanded based on the actual site structure
    // Look for dimensions, weight, cargo space, etc.
  }

  /**
   * Extract vehicle images
   */
  private extractImages(data: Partial<EVModel>): void {
    const images: string[] = [];

    // Main image
    const mainImageSrc = this.$(siteConfig.selectors.mainImage).attr("src");
    if (mainImageSrc) {
      images.push(this.resolveImageUrl(mainImageSrc));
    }

    // Gallery images
    this.$(siteConfig.selectors.galleryImages).each((_, img) => {
      const src = this.$(img).attr("src");
      if (src) {
        images.push(this.resolveImageUrl(src));
      }
    });

    if (images.length > 0) {
      data.images = [...new Set(images)]; // Remove duplicates
    }
  }

  /**
   * Extract car links from listing pages
   */
  extractCarLinks(): string[] {
    const links: string[] = [];

    this.$(siteConfig.selectors.carLinks).each((_, link) => {
      const href = this.$(link).attr("href");
      if (href) {
        const fullUrl = this.resolveUrl(href);
        if (this.isValidCarUrl(fullUrl)) {
          links.push(fullUrl);
        }
      }
    });

    return [...new Set(links)]; // Remove duplicates
  }

  /**
   * Check if URL is a valid car detail page
   */
  private isValidCarUrl(url: string): boolean {
    return url.includes("/car/") && !url.includes("#") && !url.includes("?");
  }

  /**
   * Resolve relative URLs
   */
  private resolveUrl(href: string): string {
    if (href.startsWith("http")) {
      return href;
    } else if (href.startsWith("//")) {
      return `https:${href}`;
    } else if (href.startsWith("/")) {
      return `${siteConfig.baseUrl}${href}`;
    } else {
      return `${siteConfig.baseUrl}/${href}`;
    }
  }

  /**
   * Extract additional metadata
   */
  private extractMetadata(data: Partial<EVModel>): void {
    // Set production status based on availability indicators
    data.production_status = "current"; // Default assumption

    // Look for indicators of upcoming or discontinued models
    const pageText = this.$("body").text().toLowerCase();
    if (pageText.includes("coming soon") || pageText.includes("upcoming")) {
      data.production_status = "upcoming";
    } else if (
      pageText.includes("discontinued") ||
      pageText.includes("no longer")
    ) {
      data.production_status = "discontinued";
    }
  }

  /**
   * Utility methods
   */
  private getTextContent(selector: string): string | null {
    const element = this.$(selector).first();
    return element.length > 0 ? cleanText(element.text()) : null;
  }

  private parseTitleString(title: string): {
    make?: string;
    model?: string;
    year?: number;
    trim?: string;
  } {
    // Common patterns for EV titles
    const patterns = [
      /^(\w+)\s+(.+?)\s+(20\d{2})\s*(.*)$/, // "Tesla Model 3 2024 Long Range"
      /^(20\d{2})\s+(\w+)\s+(.+?)(?:\s+(.+))?$/, // "2024 Tesla Model 3 Long Range"
      /^(\w+)\s+(.+?)(?:\s+(.+))?$/, // "Tesla Model 3 Long Range"
    ];

    for (const pattern of patterns) {
      const match = title.match(pattern);
      if (match) {
        if (pattern.source.startsWith("^(20\\d{2})")) {
          // Year first pattern
          return {
            year: parseInt(match[1]),
            make: match[2],
            model: match[3],
            trim: match[4],
          };
        } else if (pattern.source.includes("(20\\d{2})")) {
          // Year in middle pattern
          return {
            make: match[1],
            model: match[2],
            year: parseInt(match[3]),
            trim: match[4],
          };
        } else {
          // No year pattern
          return {
            make: match[1],
            model: match[2],
            trim: match[3],
          };
        }
      }
    }

    return {};
  }

  private parseDrivetrain(
    text: string
  ): "FWD" | "RWD" | "AWD" | "4WD" | undefined {
    const lower = text.toLowerCase();
    if (lower.includes("awd") || lower.includes("all-wheel")) return "AWD";
    if (lower.includes("4wd") || lower.includes("four-wheel")) return "4WD";
    if (lower.includes("fwd") || lower.includes("front-wheel")) return "FWD";
    if (lower.includes("rwd") || lower.includes("rear-wheel")) return "RWD";

    // If we can't determine the drivetrain type, return undefined
    return undefined;
  }

  private resolveImageUrl(src: string): string {
    if (src.startsWith("http")) {
      return src;
    } else if (src.startsWith("//")) {
      return `https:${src}`;
    } else if (src.startsWith("/")) {
      return `${siteConfig.baseUrl}${src}`;
    } else {
      return `${siteConfig.baseUrl}/${src}`;
    }
  }
}
