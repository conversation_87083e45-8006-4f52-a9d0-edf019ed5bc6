"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrowserManager = void 0;
const playwright_1 = require("playwright");
const logger_1 = require("../utils/logger");
const config_1 = require("../utils/config");
/**
 * Browser management for web scraping
 */
class BrowserManager {
    browser = null;
    context = null;
    config;
    constructor(config) {
        this.config = config;
    }
    /**
     * Initialize browser and context
     */
    async initialize() {
        try {
            logger_1.logger.info("Initializing browser", {
                headless: this.config.headless,
                viewport: `${this.config.viewport_width}x${this.config.viewport_height}`,
            });
            this.browser = await playwright_1.chromium.launch({
                headless: this.config.headless,
                args: [
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu",
                ],
            });
            this.context = await this.browser.newContext({
                viewport: {
                    width: this.config.viewport_width,
                    height: this.config.viewport_height,
                },
                userAgent: this.config.user_agent,
                extraHTTPHeaders: config_1.siteConfig.headers,
            });
            // Set up request interception for better performance
            await this.context.route("**/*", (route) => {
                const resourceType = route.request().resourceType();
                // Block unnecessary resources to speed up scraping
                if (["image", "stylesheet", "font", "media"].includes(resourceType)) {
                    route.abort();
                }
                else {
                    route.continue();
                }
            });
            logger_1.logger.info("Browser initialized successfully");
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to initialize browser", { error: errorMessage });
            throw error;
        }
    }
    /**
     * Create a new page with proper configuration
     */
    async createPage() {
        if (!this.context) {
            throw new Error("Browser context not initialized");
        }
        const page = await this.context.newPage();
        // Set up page event listeners
        page.on("console", (msg) => {
            if (msg.type() === "error") {
                logger_1.logger.debug("Browser console error", { message: msg.text() });
            }
        });
        page.on("pageerror", (error) => {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.debug("Page error", { error: errorMessage });
        });
        page.on("requestfailed", (request) => {
            logger_1.logger.debug("Request failed", {
                url: request.url(),
                failure: request.failure()?.errorText,
            });
        });
        return page;
    }
    /**
     * Navigate to URL with retry logic and rate limiting
     */
    async navigateToUrl(page, url) {
        try {
            // Rate limiting delay
            if (this.config.delay_between_requests > 0) {
                (0, logger_1.logRateLimitDelay)(this.config.delay_between_requests);
                await this.delay(this.config.delay_between_requests);
            }
            logger_1.logger.debug("Navigating to URL", { url });
            const response = await page.goto(url, {
                waitUntil: "domcontentloaded",
                timeout: 30000,
            });
            if (!response) {
                logger_1.logger.warn("No response received", { url });
                return false;
            }
            const status = response.status();
            if (status >= 400) {
                logger_1.logger.warn("HTTP error response", { url, status });
                return false;
            }
            // Wait for page to be fully loaded
            await page
                .waitForLoadState("networkidle", { timeout: 10000 })
                .catch(() => {
                logger_1.logger.debug("Network idle timeout, continuing anyway", { url });
            });
            logger_1.logger.debug("Successfully navigated to URL", { url, status });
            return true;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to navigate to URL", {
                url,
                error: errorMessage,
            });
            return false;
        }
    }
    /**
     * Extract page content safely
     */
    async extractPageContent(page) {
        try {
            return await page.content();
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to extract page content", { error: errorMessage });
            return "";
        }
    }
    /**
     * Take screenshot for debugging
     */
    async takeScreenshot(page, filename) {
        try {
            await page.screenshot({
                path: filename,
                fullPage: true,
            });
            logger_1.logger.debug("Screenshot saved", { filename });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to take screenshot", {
                filename,
                error: errorMessage,
            });
        }
    }
    /**
     * Check if element exists on page
     */
    async elementExists(page, selector) {
        try {
            const element = await page.$(selector);
            return element !== null;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Get text content from element
     */
    async getElementText(page, selector) {
        try {
            const element = await page.$(selector);
            if (element) {
                return await element.textContent();
            }
            return null;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.debug("Failed to get element text", {
                selector,
                error: errorMessage,
            });
            return null;
        }
    }
    /**
     * Get attribute value from element
     */
    async getElementAttribute(page, selector, attribute) {
        try {
            const element = await page.$(selector);
            if (element) {
                return await element.getAttribute(attribute);
            }
            return null;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.debug("Failed to get element attribute", {
                selector,
                attribute,
                error: errorMessage,
            });
            return null;
        }
    }
    /**
     * Get all matching elements text
     */
    async getAllElementsText(page, selector) {
        try {
            const elements = await page.$$(selector);
            const texts = [];
            for (const element of elements) {
                const text = await element.textContent();
                if (text) {
                    texts.push(text.trim());
                }
            }
            return texts;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.debug("Failed to get all elements text", {
                selector,
                error: errorMessage,
            });
            return [];
        }
    }
    /**
     * Scroll page to load dynamic content
     */
    async scrollPage(page) {
        try {
            await page.evaluate(() => {
                return new Promise((resolve) => {
                    let totalHeight = 0;
                    const distance = 100;
                    const timer = setInterval(() => {
                        const scrollHeight = document.body.scrollHeight;
                        window.scrollBy(0, distance);
                        totalHeight += distance;
                        if (totalHeight >= scrollHeight) {
                            clearInterval(timer);
                            resolve();
                        }
                    }, 100);
                });
            });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.debug("Failed to scroll page", { error: errorMessage });
        }
    }
    /**
     * Utility delay function
     */
    async delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
    /**
     * Close browser and cleanup
     */
    async close() {
        try {
            if (this.context) {
                await this.context.close();
                this.context = null;
            }
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            logger_1.logger.info("Browser closed successfully");
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger_1.logger.error("Failed to close browser", { error: errorMessage });
        }
    }
    /**
     * Check if browser is initialized
     */
    isInitialized() {
        return this.browser !== null && this.context !== null;
    }
}
exports.BrowserManager = BrowserManager;
//# sourceMappingURL=browser.js.map