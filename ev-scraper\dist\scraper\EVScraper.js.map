{"version": 3, "file": "EVScraper.js", "sourceRoot": "", "sources": ["../../src/scraper/EVScraper.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA6B;AAC7B,sDAA6B;AAC7B,uCAA2C;AAC3C,qCAAwC;AACxC,4CAAgD;AAChD,oDAA4E;AAC5E,4CAAyD;AAOzD,4CAA6C;AAC7C,0CAAyC;AAEzC;;GAEG;AACH,MAAa,SAAS;IACZ,MAAM,CAAiB;IACvB,cAAc,CAAiB;IAC/B,aAAa,CAAgB;IAC7B,QAAQ,CAAa;IACrB,cAAc,CAAkB;IAChC,WAAW,GAAc,EAAE,CAAC;IAC5B,MAAM,GAAoB,EAAE,CAAC;IAErC,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,wBAAc,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,GAAG,IAAI,sBAAa,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAU,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;YAEH,qBAAqB;YACrB,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YAEvC,6BAA6B;YAC7B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAE7C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;YACjD,CAAC;YAED,+BAA+B;YAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,uBAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEzD,2CAA2C;YAC3C,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAElC,eAAe;YACf,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAE7C,wBAAwB;YACxB,MAAM,MAAM,GAAmB;gBAC7B,OAAO,EAAE,IAAI;gBACb,cAAc,EAAE,OAAO,CAAC,MAAM;gBAC9B,kBAAkB,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC3C,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;gBAClC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACzC,YAAY,EAAE,WAAW;aAC1B,CAAC;YAEF,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,MAAM,CAAC,CAAC;YAE/D,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,MAAM,GAAmB;gBAC7B,OAAO,EAAE,KAAK;gBACd,cAAc,EAAE,CAAC;gBACjB,kBAAkB,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC3C,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;gBAClC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACzC,YAAY,EAAE,EAAE;aACjB,CAAC;YAEF,MAAM,YAAY,GAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzD,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBACtC,KAAK,EAAE,YAAY;gBACnB,MAAM;aACP,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,eAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAEpC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,uCAAuC;YACvC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CACrD,IAAI,EACJ,mBAAU,CAAC,IAAI,CAAC,OAAO,CACxB,CAAC;YAEF,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YAED,sCAAsC;YACtC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAChE,MAAM,MAAM,GAAG,IAAI,qBAAY,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;YAE1C,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAExE,kDAAkD;YAClD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC7D,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;YAEhC,sCAAsC;YACtC,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;YACzC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;YAEtD,wEAAwE;YACxE,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc;gBAC9C,CAAC,CAAC,YAAY;gBACd,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAEnE,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACzC,UAAU,EAAE,UAAU,CAAC,MAAM;gBAC7B,cAAc,EAAE,YAAY,CAAC,MAAM;gBACnC,cAAc,EAAE,YAAY,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM;gBAC1D,QAAQ,EAAE,aAAa,CAAC,MAAM;aAC/B,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzD,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,IAAS;QAC1C,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC;QAE7C,OAAO,WAAW,GAAG,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC;gBACH,0BAA0B;gBAC1B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAC5D,IAAI,EACJ,mBAAU,CAAC,SAAS,CAAC,QAAQ,CAC9B,CAAC;gBAEF,IAAI,CAAC,cAAc,EAAE,CAAC;oBACpB,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;oBACpD,MAAM;gBACR,CAAC;gBAED,kBAAkB;gBAClB,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,IAAI;qBACP,gBAAgB,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;qBACnD,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;gBAEnB,WAAW,EAAE,CAAC;gBACd,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;gBAEnD,+BAA+B;gBAC/B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAChE,MAAM,MAAM,GAAG,IAAI,qBAAY,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;gBAE1C,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;gBAC1B,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;oBACtC,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,QAAQ,CAAC,MAAM;iBACvB,CAAC,CAAC;gBAEH,8BAA8B;gBAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACzD,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBACnC,IAAI,EAAE,WAAW;oBACjB,KAAK,EAAE,YAAY;iBACpB,CAAC,CAAC;gBACH,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,IAAc;QACpC,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,iCAAiC;QACjC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxE,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;gBACjC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAC/C,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACtC,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,IAAc;QACxC,MAAM,KAAK,GAAG,IAAA,iBAAM,EAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAE1D,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CACxC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAC1C,CAAC;QAEF,MAAM,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,GAAW;QAC1C,IAAI,CAAC;YACH,MAAM,IAAA,iBAAM,EAAC,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;gBAC5C,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;gBAChC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;gBACnC,eAAe,EAAE,CAAC,KAAK,EAAE,EAAE;oBACzB,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;wBAClC,GAAG;wBACH,OAAO,EAAE,KAAK,CAAC,aAAa;wBAC5B,WAAW,EAAE,KAAK,CAAC,WAAW;wBAC9B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;qBAC9D,CAAC,CAAC;gBACL,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,GAAW;QACvC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;QAEpD,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAEnE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,uBAAuB;YACvB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEhE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YAED,gBAAgB;YAChB,MAAM,MAAM,GAAG,IAAI,qBAAY,CAAC,IAAI,CAAC,CAAC;YACtC,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAE1C,gBAAgB;YAChB,MAAM,UAAU,GAAG,IAAA,4BAAe,EAAC,MAAM,CAAC,CAAC;YAC3C,IAAA,iCAAoB,EAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YAEtC,IAAI,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBACjD,mBAAmB;gBACnB,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBACtE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;gBAErD,yCAAyC;gBACzC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAC9C,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE;oBAC1C,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI;oBACjC,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC,KAAK;iBACpC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,uBAAuB;gBACvB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAC5B,GAAG,EACH,KAAK,EACL,SAAS,EACT,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7B,CAAC;gBACF,MAAM,IAAI,KAAK,CACb,2BAA2B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC1D,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC,CAAC,2BAA2B;QAC1C,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CACzB,GAAW,EACX,KAAU,EACV,UAAkB;QAElB,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,MAAM,aAAa,GAAkB;YACnC,GAAG;YACH,aAAa,EAAE,YAAY;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,WAAW,EAAE,UAAU;SACxB,CAAC;QAEF,mCAAmC;QACnC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAEpE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW;QACvB,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,CAAC;YACH,oBAAoB;YACpB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;oBAC1B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAClD,IAAI,CAAC,WAAW,CACjB,CAAC;oBACF,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7B,CAAC;gBAED,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACzB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACrE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;gBAED,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACzB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACrE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC;YAED,qBAAqB;YACrB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpE,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;YAED,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAChB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzD,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;YAChE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC;CACF;AAxYD,8BAwYC"}