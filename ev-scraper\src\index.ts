#!/usr/bin/env node

import yargs from 'yargs'
import { hideBin } from 'yargs/helpers'
import { EVScraper } from './scraper/EVScraper'
import { defaultConfig } from './utils/config'
import { logger, logScrapingStart, logScrapingEnd } from './utils/logger'
import { ScrapingConfig } from './types'
import robotsParser from 'robots-parser'
import fetch from 'node-fetch'

/**
 * CLI interface for the EV scraper
 */
async function main() {
  const argv = await yargs(hideBin(process.argv))
    .usage('Usage: $0 [options]')
    .option('output', {
      alias: 'o',
      type: 'string',
      description: 'Output directory for scraped data',
      default: defaultConfig.output_directory
    })
    .option('delay', {
      alias: 'd',
      type: 'number',
      description: 'Delay between requests in milliseconds',
      default: defaultConfig.delay_between_requests
    })
    .option('concurrent', {
      alias: 'c',
      type: 'number',
      description: 'Maximum concurrent requests',
      default: defaultConfig.max_concurrent_requests
    })
    .option('retries', {
      alias: 'r',
      type: 'number',
      description: 'Maximum retry attempts',
      default: defaultConfig.max_retries
    })
    .option('headless', {
      type: 'boolean',
      description: 'Run browser in headless mode',
      default: defaultConfig.headless
    })
    .option('max-pages', {
      alias: 'p',
      type: 'number',
      description: 'Maximum pages to scrape'
    })
    .option('makes', {
      alias: 'm',
      type: 'string',
      description: 'Comma-separated list of specific makes to scrape (e.g., "Tesla,BMW,Audi")'
    })
    .option('year-start', {
      type: 'number',
      description: 'Start year for filtering vehicles'
    })
    .option('year-end', {
      type: 'number',
      description: 'End year for filtering vehicles'
    })
    .option('json', {
      type: 'boolean',
      description: 'Save data as JSON',
      default: defaultConfig.save_json
    })
    .option('csv', {
      type: 'boolean',
      description: 'Save data as CSV',
      default: defaultConfig.save_csv
    })
    .option('sql', {
      type: 'boolean',
      description: 'Save data as SQL',
      default: defaultConfig.save_sql
    })
    .option('check-robots', {
      type: 'boolean',
      description: 'Check robots.txt before scraping',
      default: true
    })
    .option('verbose', {
      alias: 'v',
      type: 'boolean',
      description: 'Enable verbose logging',
      default: false
    })
    .help()
    .alias('help', 'h')
    .example('$0', 'Scrape with default settings')
    .example('$0 --makes "Tesla,BMW" --max-pages 5', 'Scrape only Tesla and BMW models, max 5 pages')
    .example('$0 --delay 5000 --concurrent 1', 'Slow scraping with 5s delay and 1 concurrent request')
    .example('$0 --output ./my-data --json --csv', 'Save to custom directory in JSON and CSV formats')
    .argv

  // Set log level based on verbose flag
  if (argv.verbose) {
    logger.level = 'debug'
  }

  // Build configuration from CLI arguments
  const config: ScrapingConfig = {
    ...defaultConfig,
    output_directory: argv.output,
    delay_between_requests: argv.delay,
    max_concurrent_requests: argv.concurrent,
    max_retries: argv.retries,
    headless: argv.headless,
    max_pages: argv['max-pages'],
    specific_makes: argv.makes ? argv.makes.split(',').map(m => m.trim()) : undefined,
    year_range: (argv['year-start'] && argv['year-end']) ? {
      start: argv['year-start'],
      end: argv['year-end']
    } : undefined,
    save_json: argv.json,
    save_csv: argv.csv,
    save_sql: argv.sql
  }

  try {
    // Check robots.txt if requested
    if (argv['check-robots']) {
      const robotsAllowed = await checkRobotsPermission()
      if (!robotsAllowed) {
        logger.warn('Robots.txt disallows scraping. Use --no-check-robots to override.')
        process.exit(1)
      }
    }

    // Log scraping start
    logScrapingStart(config)

    // Create and run scraper
    const scraper = new EVScraper(config)
    const result = await scraper.scrape()

    // Log results
    logScrapingEnd(result)

    // Print summary to console
    printSummary(result)

    // Exit with appropriate code
    process.exit(result.success ? 0 : 1)

  } catch (error) {
    logger.error('Scraping failed with unhandled error', {
      error: error.message,
      stack: error.stack
    })
    
    console.error('\n❌ Scraping failed:', error.message)
    process.exit(1)
  }
}

/**
 * Check robots.txt permission
 */
async function checkRobotsPermission(): Promise<boolean> {
  try {
    logger.info('Checking robots.txt permission')
    
    const response = await fetch('https://ev-database.org/robots.txt')
    const robotsTxt = await response.text()
    
    const robots = robotsParser('https://ev-database.org/robots.txt', robotsTxt)
    const userAgent = defaultConfig.user_agent || '*'
    
    // Check if scraping is allowed for the car pages
    const isAllowed = robots.isAllowed('https://ev-database.org/car', userAgent)
    
    if (isAllowed) {
      logger.info('Robots.txt allows scraping')
    } else {
      logger.warn('Robots.txt disallows scraping')
    }
    
    return isAllowed
    
  } catch (error) {
    logger.warn('Failed to check robots.txt, proceeding anyway', {
      error: error.message
    })
    return true // Allow scraping if robots.txt check fails
  }
}

/**
 * Print summary to console
 */
function printSummary(result: any) {
  console.log('\n' + '='.repeat(60))
  console.log('🚗 EV SCRAPING SUMMARY')
  console.log('='.repeat(60))
  
  if (result.success) {
    console.log('✅ Status: SUCCESS')
  } else {
    console.log('❌ Status: FAILED')
  }
  
  console.log(`📊 Total Vehicles: ${result.total_vehicles}`)
  console.log(`✅ Successful Scrapes: ${result.successful_scrapes}`)
  console.log(`❌ Failed Scrapes: ${result.failed_scrapes}`)
  
  if (result.successful_scrapes > 0) {
    const successRate = ((result.successful_scrapes / result.total_vehicles) * 100).toFixed(1)
    console.log(`📈 Success Rate: ${successRate}%`)
  }
  
  console.log(`⏱️  Execution Time: ${(result.execution_time_ms / 1000).toFixed(1)}s`)
  
  if (result.output_files && result.output_files.length > 0) {
    console.log('\n📁 Output Files:')
    result.output_files.forEach((file: string) => {
      console.log(`   • ${file}`)
    })
  }
  
  if (result.errors && result.errors.length > 0) {
    console.log(`\n⚠️  Errors: ${result.errors.length}`)
    console.log('   Check the error log file for details.')
  }
  
  console.log('\n' + '='.repeat(60))
}

/**
 * Handle process signals for graceful shutdown
 */
process.on('SIGINT', () => {
  logger.info('Received SIGINT, shutting down gracefully...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  logger.info('Received SIGTERM, shutting down gracefully...')
  process.exit(0)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', {
    reason: reason,
    promise: promise
  })
})

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', {
    error: error.message,
    stack: error.stack
  })
  process.exit(1)
})

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    logger.error('Main function failed', {
      error: error.message,
      stack: error.stack
    })
    process.exit(1)
  })
}
