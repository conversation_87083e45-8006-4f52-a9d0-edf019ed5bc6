{
  service: 'ev-scraper',
  error: 'Failed to load car listing page',
  level: 'error',
  message: 'Failed to discover car URLs',
  timestamp: '2025-07-16 17:53:48'
}
{
  service: 'ev-scraper',
  error: 'Failed to load car listing page',
  result: {
    success: false,
    total_vehicles: 0,
    successful_scrapes: 0,
    failed_scrapes: 0,
    errors: [],
    execution_time_ms: 3283,
    output_files: []
  },
  level: 'error',
  message: 'Scraping session failed',
  timestamp: '2025-07-16 17:53:48'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3034/Tesla-Model-3-Long-Range-RWD',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at runNextTicks (node:internal/process/task_queues:65:5)\n' +
    '    at processImmediate (node:internal/timers:459:9)\n' +
    '    at process.topLevelDomainCallback (node:domain:161:15)\n' +
    '    at process.callbackTrampoline (node:internal/async_hooks:128:24)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:56:16',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3103/Tesla-Model-Y-RWD',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:56:17',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1708/MG-MG4-Electric-64-kWh',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:56:17',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3139/Mercedes-Benz-CLA-250plus',
  error: 'Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: make, Missing required field: model, Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:05',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1472/BMW-iX-xDrive40',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:06',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/3104/Tesla-Model-Y-Long-Range-AWD',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:06',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1782/BYD-ATTO-3',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at runNextTicks (node:internal/process/task_queues:65:5)\n' +
    '    at processImmediate (node:internal/timers:459:9)\n' +
    '    at process.topLevelDomainCallback (node:domain:161:15)\n' +
    '    at process.callbackTrampoline (node:internal/async_hooks:128:24)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:54',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1285/Fiat-500e-Hatchback-42-kWh',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:55',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/1516/CUPRA-Born-150-kW---58-kWh',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:57:56',
  level: 'error',
  message: 'Scraping error'
}
{
  service: 'ev-scraper',
  url: 'https://ev-database.org/car/2212/Kia-EV3-Long-Range',
  error: 'Data validation failed: Missing required field: year',
  stack: 'Error: Data validation failed: Missing required field: year\n' +
    '    at EVScraper.scrapeSingleCar (C:\\Users\\<USER>\\Desktop\\ev-app\\ev-scraper\\src\\scraper\\EVScraper.ts:307:15)\n' +
    '    at async RetryOperation._fn (file:///C:/Users/<USER>/Desktop/ev-app/ev-scraper/node_modules/p-retry/index.js:55:20)',
  retryCount: 3,
  timestamp: '2025-07-16 17:58:44',
  level: 'error',
  message: 'Scraping error'
}
