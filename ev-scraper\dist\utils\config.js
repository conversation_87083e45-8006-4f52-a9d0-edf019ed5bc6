"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.outputConfig = exports.validationRules = exports.siteConfig = exports.defaultConfig = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
// Load environment variables
dotenv_1.default.config();
/**
 * Default scraping configuration
 * Can be overridden by environment variables or command line arguments
 */
exports.defaultConfig = {
    // Rate limiting - be respectful to the target website
    delay_between_requests: parseInt(process.env.DELAY_BETWEEN_REQUESTS || '2000'), // 2 seconds
    max_concurrent_requests: parseInt(process.env.MAX_CONCURRENT_REQUESTS || '3'), // 3 concurrent requests
    // Retry logic
    max_retries: parseInt(process.env.MAX_RETRIES || '3'),
    retry_delay: parseInt(process.env.RETRY_DELAY || '5000'), // 5 seconds
    // Browser settings
    headless: process.env.HEADLESS !== 'false', // Default to headless
    user_agent: process.env.USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    viewport_width: parseInt(process.env.VIEWPORT_WIDTH || '1920'),
    viewport_height: parseInt(process.env.VIEWPORT_HEIGHT || '1080'),
    // Output settings
    output_directory: process.env.OUTPUT_DIR || path_1.default.join(process.cwd(), 'output'),
    save_json: process.env.SAVE_JSON !== 'false',
    save_csv: process.env.SAVE_CSV !== 'false',
    save_sql: process.env.SAVE_SQL !== 'false',
    // Scraping scope
    max_pages: process.env.MAX_PAGES ? parseInt(process.env.MAX_PAGES) : undefined,
    specific_makes: process.env.SPECIFIC_MAKES ? process.env.SPECIFIC_MAKES.split(',').map(m => m.trim()) : undefined,
    year_range: process.env.YEAR_START && process.env.YEAR_END ? {
        start: parseInt(process.env.YEAR_START),
        end: parseInt(process.env.YEAR_END)
    } : undefined
};
/**
 * Target website configuration
 */
exports.siteConfig = {
    baseUrl: 'https://ev-database.org',
    robotsTxtUrl: 'https://ev-database.org/robots.txt',
    // URL patterns
    urls: {
        carList: 'https://ev-database.org/car',
        carDetail: (id) => `https://ev-database.org/car/${id}`,
        search: 'https://ev-database.org/car',
        brands: 'https://ev-database.org/car'
    },
    // CSS selectors for data extraction
    selectors: {
        // Car listing page
        carLinks: 'a[href*="/car/"]',
        carCards: '.car-card, .vehicle-card, .listing-item',
        pagination: '.pagination a, .page-numbers a',
        nextPage: '.pagination .next, .page-numbers .next',
        // Car detail page
        carTitle: 'h1, .car-title, .vehicle-name',
        specifications: '.specifications, .specs, .car-specs',
        specTable: '.spec-table, .specifications-table',
        specRows: 'tr, .spec-row',
        specLabel: 'td:first-child, .spec-label, .label',
        specValue: 'td:last-child, .spec-value, .value',
        // Specific data fields
        price: '.price, .msrp, .starting-price',
        range: '.range, .epa-range, .wltp-range',
        battery: '.battery, .battery-capacity, .kwh',
        charging: '.charging, .charging-speed, .dc-fast',
        performance: '.performance, .acceleration, .0-60',
        efficiency: '.efficiency, .mpge, .consumption',
        // Images
        mainImage: '.main-image img, .hero-image img, .car-image img',
        galleryImages: '.gallery img, .images img, .photos img',
        // Additional info
        bodyType: '.body-type, .category, .segment',
        drivetrain: '.drivetrain, .drive-type, .awd',
        seating: '.seating, .seats, .capacity'
    },
    // Common data patterns and units
    patterns: {
        price: /\$?[\d,]+/,
        range: /(\d+)\s*(miles?|mi|km)/i,
        battery: /(\d+(?:\.\d+)?)\s*kwh/i,
        charging: /(\d+(?:\.\d+)?)\s*kw/i,
        acceleration: /(\d+(?:\.\d+)?)\s*sec/i,
        speed: /(\d+)\s*mph/i,
        efficiency: /(\d+)\s*mpge?/i,
        year: /20\d{2}/,
        power: /(\d+)\s*(hp|kw)/i
    },
    // Request headers
    headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }
};
/**
 * Validation rules for scraped data
 */
exports.validationRules = {
    required_fields: ['make', 'model', 'year'],
    ranges: {
        year: { min: 2010, max: new Date().getFullYear() + 2 },
        price: { min: 1000, max: 500000 }, // $10 - $5M (in dollars)
        battery_capacity: { min: 10, max: 300 }, // 10-300 kWh
        range_miles: { min: 50, max: 800 }, // 50-800 miles
        charging_speed: { min: 1, max: 500 }, // 1-500 kW
        acceleration: { min: 1, max: 20 }, // 1-20 seconds
        top_speed: { min: 50, max: 300 }, // 50-300 mph
        efficiency: { min: 50, max: 200 } // 50-200 MPGe
    },
    valid_body_types: [
        'sedan', 'suv', 'hatchback', 'coupe', 'convertible',
        'wagon', 'truck', 'van', 'crossover', 'roadster'
    ],
    valid_drivetrains: ['FWD', 'RWD', 'AWD', '4WD'],
    valid_production_status: ['current', 'upcoming', 'discontinued']
};
/**
 * Output file naming conventions
 */
exports.outputConfig = {
    fileNames: {
        json: (timestamp) => `ev-data-${timestamp}.json`,
        csv: (timestamp) => `ev-data-${timestamp}.csv`,
        sql: (timestamp) => `ev-data-${timestamp}.sql`,
        errors: (timestamp) => `scraping-errors-${timestamp}.json`,
        summary: (timestamp) => `scraping-summary-${timestamp}.json`
    },
    timestamp: () => new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
};
//# sourceMappingURL=config.js.map