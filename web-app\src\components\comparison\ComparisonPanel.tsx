'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { X, ChevronUp, ChevronDown, GitCompare, Trash2, ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useComparison } from '@/contexts/ComparisonContext'
import { useComparisonActions } from '@/hooks/useComparisonActions'
import { formatPrice, formatRange } from '@/utils/ev-buyer-guide'
import { PLACEHOLDER_IMAGES } from '@/constants/ev-buyer-guide'
import type { EVModel } from '../../../types'

export function ComparisonPanel() {
  const { state, togglePanel, closePanel } = useComparison()
  const { removeFromComparison, clearComparison } = useComparisonActions()
  const [isMinimized, setIsMinimized] = useState(false)

  // Early return if no models
  if (state.models.length === 0) {
    return null
  }

  // Ensure panel is visible when models exist
  const shouldShow = state.models.length > 0

  const handleToggleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  const handleCompareAll = () => {
    // Navigate to comparison page with all models
    const modelIds = state.models.map((m) => m.id).join(',')
    window.open(`/compare?models=${modelIds}`, '_blank')
  }

  return (
    <>
      {/* Mobile backdrop */}
      {(state.isOpen || shouldShow) && (
        <div
          className="fixed inset-0 z-[9998] bg-black/20 backdrop-blur-sm transition-opacity duration-300 sm:hidden"
          onClick={closePanel}
        />
      )}

      {/* Panel */}
      <div
        className={cn(
          // Base positioning and sizing
          'fixed z-[9999] transition-all duration-300 ease-in-out',
          // Mobile: full width at bottom with rounded top corners
          'bottom-0 left-0 right-0 max-h-[80vh] w-full',
          // Desktop: bottom-right corner
          'sm:bottom-4 sm:left-auto sm:right-4 sm:max-h-[calc(100vh-2rem)] sm:w-96 sm:max-w-[calc(100vw-2rem)]',
          // Visibility states - show when panel should be open OR when models exist
          state.isOpen || shouldShow
            ? 'translate-y-0 opacity-100'
            : 'pointer-events-none translate-y-full opacity-0'
        )}
      >
        <Card className="flex h-full flex-col rounded-b-none rounded-t-lg border-electric-200 bg-white shadow-2xl dark:border-electric-800 dark:bg-gray-900 sm:rounded-lg sm:rounded-b-lg">
          <CardHeader className="shrink-0 pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-lg">
                <GitCompare className="h-5 w-5 text-electric-600" />
                Compare EVs
                <Badge variant="secondary" className="ml-2">
                  {state.models.length}/4
                </Badge>
              </CardTitle>

              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleToggleMinimize}
                  className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                  title={isMinimized ? 'Expand panel' : 'Minimize panel'}
                >
                  {isMinimized ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={closePanel}
                  className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                  title="Close panel"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>

          {!isMinimized && (
            <CardContent className="flex flex-1 flex-col space-y-4 overflow-hidden">
              {/* Model List */}
              <div className="-mr-2 min-h-0 flex-1 space-y-3 overflow-y-auto pr-2">
                {state.models.map((model) => (
                  <div
                    key={model.id}
                    className="flex items-center gap-3 rounded-lg border p-3 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    {/* Model Image */}
                    <div className="relative h-12 w-16 shrink-0 overflow-hidden rounded bg-gray-100 dark:bg-gray-800">
                      <Image
                        src={model.images?.[0] || PLACEHOLDER_IMAGES.ev_model}
                        alt={`${model.make} ${model.model}`}
                        fill
                        className="object-cover"
                        sizes="64px"
                      />
                    </div>

                    {/* Model Info */}
                    <div className="min-w-0 flex-1">
                      <div className="truncate text-sm font-medium text-gray-900 dark:text-gray-100">
                        {model.make} {model.model}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {formatPrice(model.price_msrp)} • {formatRange(model.range_epa_miles)}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex shrink-0 items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                        className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="View details"
                      >
                        <Link href={`/ev-models/${model.id}`}>
                          <ExternalLink className="h-3 w-3" />
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFromComparison(model.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-900/20"
                        title="Remove from comparison"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Actions */}
              <div className="flex shrink-0 gap-2 border-t pt-3">
                <Button
                  onClick={handleCompareAll}
                  className="flex-1 bg-electric-600 hover:bg-electric-700"
                  disabled={state.models.length < 2}
                >
                  <GitCompare className="mr-2 h-4 w-4" />
                  Compare All
                </Button>
                <Button
                  variant="outline"
                  onClick={clearComparison}
                  className="text-red-600 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-900/20"
                  title="Clear all models"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              {/* Help Text */}
              {state.models.length < 2 && (
                <p className="shrink-0 text-center text-xs text-gray-600 dark:text-gray-400">
                  Add at least 2 models to start comparing
                </p>
              )}
            </CardContent>
          )}
        </Card>
      </div>
    </>
  )
}
