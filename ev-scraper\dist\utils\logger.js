"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logRetryAttempt = exports.logRateLimitDelay = exports.logScrapingEnd = exports.logScrapingStart = exports.ProgressLogger = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
// Ensure logs directory exists
const logsDir = path_1.default.join(process.cwd(), 'logs');
fs_extra_1.default.ensureDirSync(logsDir);
// Custom log format
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.prettyPrint());
// Console format for development
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({
    format: 'HH:mm:ss'
}), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        msg += `\n${JSON.stringify(meta, null, 2)}`;
    }
    return msg;
}));
// Create logger instance
exports.logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    defaultMeta: { service: 'ev-scraper' },
    transports: [
        // Write all logs to combined.log
        new winston_1.default.transports.File({
            filename: path_1.default.join(logsDir, 'combined.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // Write error logs to error.log
        new winston_1.default.transports.File({
            filename: path_1.default.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // Write scraping logs to scraping.log
        new winston_1.default.transports.File({
            filename: path_1.default.join(logsDir, 'scraping.log'),
            level: 'debug',
            maxsize: 10485760, // 10MB
            maxFiles: 3,
        })
    ]
});
// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
    exports.logger.add(new winston_1.default.transports.Console({
        format: consoleFormat,
        level: 'debug'
    }));
}
// Progress logging utilities
class ProgressLogger {
    startTime;
    totalItems;
    processedItems = 0;
    successfulItems = 0;
    failedItems = 0;
    constructor(totalItems) {
        this.startTime = new Date();
        this.totalItems = totalItems;
        this.logStart();
    }
    logStart() {
        exports.logger.info('Scraping session started', {
            totalItems: this.totalItems,
            startTime: this.startTime.toISOString()
        });
    }
    logProgress(url, success, details) {
        this.processedItems++;
        if (success) {
            this.successfulItems++;
        }
        else {
            this.failedItems++;
        }
        const progress = (this.processedItems / this.totalItems * 100).toFixed(1);
        const elapsed = Date.now() - this.startTime.getTime();
        const avgTimePerItem = elapsed / this.processedItems;
        const estimatedTimeRemaining = (this.totalItems - this.processedItems) * avgTimePerItem;
        exports.logger.info('Progress update', {
            url,
            success,
            progress: `${progress}%`,
            processed: this.processedItems,
            total: this.totalItems,
            successful: this.successfulItems,
            failed: this.failedItems,
            elapsedMs: elapsed,
            estimatedRemainingMs: estimatedTimeRemaining,
            ...details
        });
        // Log every 10% or every 50 items, whichever is more frequent
        const logInterval = Math.min(Math.ceil(this.totalItems / 10), 50);
        if (this.processedItems % logInterval === 0) {
            this.logSummary();
        }
    }
    logError(url, error, retryCount = 0) {
        this.failedItems++;
        this.processedItems++;
        exports.logger.error('Scraping error', {
            url,
            error: error.message,
            stack: error.stack,
            retryCount,
            timestamp: new Date().toISOString()
        });
    }
    logSummary() {
        const elapsed = Date.now() - this.startTime.getTime();
        const progress = (this.processedItems / this.totalItems * 100).toFixed(1);
        const successRate = (this.successfulItems / this.processedItems * 100).toFixed(1);
        exports.logger.info('Scraping summary', {
            progress: `${progress}%`,
            processed: this.processedItems,
            total: this.totalItems,
            successful: this.successfulItems,
            failed: this.failedItems,
            successRate: `${successRate}%`,
            elapsedMs: elapsed,
            avgTimePerItemMs: elapsed / this.processedItems
        });
    }
    logCompletion() {
        const elapsed = Date.now() - this.startTime.getTime();
        const successRate = (this.successfulItems / this.totalItems * 100).toFixed(1);
        exports.logger.info('Scraping session completed', {
            totalItems: this.totalItems,
            successful: this.successfulItems,
            failed: this.failedItems,
            successRate: `${successRate}%`,
            totalTimeMs: elapsed,
            avgTimePerItemMs: elapsed / this.totalItems,
            startTime: this.startTime.toISOString(),
            endTime: new Date().toISOString()
        });
    }
}
exports.ProgressLogger = ProgressLogger;
// Utility functions for structured logging
const logScrapingStart = (config) => {
    exports.logger.info('Starting EV data scraping', {
        config,
        timestamp: new Date().toISOString()
    });
};
exports.logScrapingStart = logScrapingStart;
const logScrapingEnd = (results) => {
    exports.logger.info('EV data scraping completed', {
        results,
        timestamp: new Date().toISOString()
    });
};
exports.logScrapingEnd = logScrapingEnd;
const logRateLimitDelay = (delayMs) => {
    exports.logger.debug('Rate limit delay', {
        delayMs,
        timestamp: new Date().toISOString()
    });
};
exports.logRateLimitDelay = logRateLimitDelay;
const logRetryAttempt = (url, attempt, maxRetries) => {
    exports.logger.warn('Retry attempt', {
        url,
        attempt,
        maxRetries,
        timestamp: new Date().toISOString()
    });
};
exports.logRetryAttempt = logRetryAttempt;
//# sourceMappingURL=logger.js.map