'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  DollarSign, 
  Zap, 
  Car, 
  Users, 
  ArrowRight,
  BarChart3,
  Award,
  Target
} from 'lucide-react'
import Link from 'next/link'

interface HomepageStats {
  totalModels: number
  totalManufacturers: number
  averageRange: number
  averagePrice: number
}

interface MarketInsightsSectionProps {
  stats: HomepageStats | null
  loading?: boolean
}

export function MarketInsightsSection({ stats, loading }: MarketInsightsSectionProps) {
  if (loading) {
    return (
      <section className="bg-white py-16 dark:bg-gray-900">
        <div className="container">
          <div className="animate-pulse">
            <div className="mx-auto h-8 w-64 bg-gray-300 rounded mb-4"></div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-300 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Default stats if none provided
  const displayStats = stats || {
    totalModels: 150,
    totalManufacturers: 25,
    averageRange: 285,
    averagePrice: 52000
  }

  const insights = [
    {
      id: 'total-models',
      title: 'EV Models',
      value: displayStats.totalModels.toLocaleString(),
      subtitle: 'Available to explore',
      icon: <Car className="h-6 w-6" />,
      color: 'bg-blue-500',
      trend: '+12% this year'
    },
    {
      id: 'manufacturers',
      title: 'Manufacturers',
      value: displayStats.totalManufacturers.toString(),
      subtitle: 'Leading EV brands',
      icon: <Users className="h-6 w-6" />,
      color: 'bg-green-500',
      trend: '+5 new brands'
    },
    {
      id: 'average-range',
      title: 'Average Range',
      value: `${displayStats.averageRange}`,
      subtitle: 'Miles per charge',
      icon: <Zap className="h-6 w-6" />,
      color: 'bg-purple-500',
      trend: '+15% vs 2023'
    },
    {
      id: 'average-price',
      title: 'Average Price',
      value: `$${displayStats.averagePrice.toLocaleString()}`,
      subtitle: 'Starting MSRP',
      icon: <DollarSign className="h-6 w-6" />,
      color: 'bg-amber-500',
      trend: '-8% vs 2023'
    }
  ]

  const marketTrends = [
    {
      title: 'Range is Increasing',
      description: 'New EVs average 285+ miles per charge',
      icon: <TrendingUp className="h-5 w-5 text-green-600" />,
      stat: '+15%'
    },
    {
      title: 'Prices are Stabilizing',
      description: 'More affordable options entering market',
      icon: <Target className="h-5 w-5 text-blue-600" />,
      stat: '-8%'
    },
    {
      title: 'More Choices',
      description: 'Record number of new models launched',
      icon: <Award className="h-5 w-5 text-purple-600" />,
      stat: '+25'
    }
  ]

  return (
    <section className="bg-white py-16 dark:bg-gray-900">
      <div className="container">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <Badge variant="secondary" className="mb-4">
            <BarChart3 className="mr-1 h-3 w-3" />
            Market Insights
          </Badge>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white md:text-4xl">
            EV Market at a Glance
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            Real-time insights from our comprehensive electric vehicle database
          </p>
        </div>

        {/* Stats Grid */}
        <div className="mb-12 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {insights.map((insight) => (
            <Card key={insight.id} className="border-0 shadow-lg transition-all hover:shadow-xl">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`rounded-lg p-2 ${insight.color} text-white`}>
                        {insight.icon}
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                          {insight.value}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {insight.title}
                        </p>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">
                      {insight.subtitle}
                    </p>
                    <Badge variant="outline" className="text-xs">
                      {insight.trend}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Market Trends */}
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Trends Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-electric-600" />
                Market Trends
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {marketTrends.map((trend, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                  <div className="mt-0.5">{trend.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-sm">{trend.title}</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {trend.description}
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {trend.stat}
                  </Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-electric-600" />
                Explore the Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <Link href="/ev-models?featured=true">
                  <Button variant="outline" className="w-full justify-start group">
                    <Award className="mr-2 h-4 w-4" />
                    Featured Models
                    <ArrowRight className="ml-auto h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
                
                <Link href="/ev-models?bestValue=true">
                  <Button variant="outline" className="w-full justify-start group">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Best Value EVs
                    <ArrowRight className="ml-auto h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
                
                <Link href="/ev-models?rangeMin=300">
                  <Button variant="outline" className="w-full justify-start group">
                    <Zap className="mr-2 h-4 w-4" />
                    Long Range EVs
                    <ArrowRight className="ml-auto h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>

              <div className="pt-4 border-t">
                <Link href="/ev-models">
                  <Button className="w-full bg-electric-600 hover:bg-electric-700 group">
                    Browse All EVs
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
