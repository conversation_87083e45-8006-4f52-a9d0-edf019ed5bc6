'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Search, TrendingUp, Zap, Users, Car, Leaf, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface SearchHeroSectionProps {
  totalModels?: number
}

export function SearchHeroSection({ totalModels }: SearchHeroSectionProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const router = useRouter()

  const quickSearches = [
    {
      id: 'family-suv',
      label: 'Family SUVs',
      icon: <Users className="h-4 w-4" />,
      query: 'family SUV electric vehicle',
      description: 'Spacious EVs for families',
      filters: { bodyType: 'suv', seatingCapacity: '5+' }
    },
    {
      id: 'budget-friendly',
      label: 'Under $40k',
      icon: <TrendingUp className="h-4 w-4" />,
      query: 'affordable electric car under 40000',
      description: 'Budget-friendly EVs',
      filters: { priceMax: '4000000' }
    },
    {
      id: 'long-range',
      label: 'Long Range',
      icon: <Zap className="h-4 w-4" />,
      query: 'long range electric vehicle 300 miles',
      description: '300+ mile range',
      filters: { rangeMin: '300' }
    },
    {
      id: 'luxury',
      label: 'Luxury EVs',
      icon: <Car className="h-4 w-4" />,
      query: 'luxury premium electric vehicle',
      description: 'Premium electric cars',
      filters: { priceMin: '7000000' }
    }
  ]

  const trendingSearches = [
    'Tesla Model 3',
    'Ford Mustang Mach-E',
    'Hyundai IONIQ 5',
    'BMW iX',
    'Rivian R1T',
    'Lucid Air'
  ]

  const handleSearch = (query: string, filters?: Record<string, string>) => {
    if (!query.trim()) return

    const params = new URLSearchParams()
    if (query) params.set('search', query)
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        params.set(key, value)
      })
    }

    router.push(`/ev-models?${params.toString()}`)
  }

  const handleQuickSearch = (quickSearch: typeof quickSearches[0]) => {
    handleSearch(quickSearch.query, quickSearch.filters)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(searchQuery)
    }
  }

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-electric-50 via-green-50 to-blue-50 py-20 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      <div className="container relative">
        <div className="mx-auto max-w-4xl text-center">
          {/* Header */}
          <Badge variant="secondary" className="mb-6">
            <Leaf className="mr-1 h-3 w-3" />
            {totalModels ? `${totalModels}+ Electric Vehicles` : 'Electric Vehicle Marketplace'}
          </Badge>
          
          <h1 className="mb-6 text-4xl font-bold tracking-tight text-gray-900 dark:text-white md:text-6xl">
            Find Your Perfect{' '}
            <span className="bg-gradient-to-r from-electric-600 to-green-600 bg-clip-text text-transparent">
              Electric Vehicle
            </span>
          </h1>
          
          <p className="mb-8 text-xl text-gray-600 dark:text-gray-300">
            Discover, compare, and choose from the world's most comprehensive EV database.
            Get personalized recommendations based on your needs and lifestyle.
          </p>

          {/* Search Bar */}
          <div className="relative mb-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search for EVs... (e.g., 'family SUV under $50k' or 'Tesla Model 3')"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                onFocus={() => setShowSuggestions(true)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                className="h-14 pl-12 pr-32 text-lg"
              />
              <Button 
                onClick={() => handleSearch(searchQuery)}
                className="absolute right-2 top-1/2 h-10 -translate-y-1/2"
              >
                Search
              </Button>
            </div>

            {/* Search Suggestions */}
            {showSuggestions && (
              <Card className="absolute top-full left-0 right-0 z-10 mt-2 border shadow-lg">
                <CardContent className="p-4">
                  <div className="mb-3">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                      Trending Searches
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {trendingSearches.map((search) => (
                        <Button
                          key={search}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSearch(search)}
                          className="h-8 text-xs"
                        >
                          {search}
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Quick Search Options */}
          <div className="mb-8">
            <p className="mb-4 text-sm font-medium text-gray-700 dark:text-gray-300">
              Popular Searches:
            </p>
            <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
              {quickSearches.map((quickSearch) => (
                <Button
                  key={quickSearch.id}
                  variant="outline"
                  onClick={() => handleQuickSearch(quickSearch)}
                  className="h-auto justify-start p-4 text-left transition-all hover:scale-105"
                >
                  <div className="flex items-center gap-3">
                    <div className="text-electric-600">{quickSearch.icon}</div>
                    <div>
                      <div className="font-semibold">{quickSearch.label}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {quickSearch.description}
                      </div>
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>

          {/* Primary CTAs */}
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Link href="/ev-models">
              <Button size="lg" className="bg-electric-600 hover:bg-electric-700 group">
                <Car className="mr-2 h-4 w-4" />
                Browse All EVs
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
            <Link href="/compare">
              <Button size="lg" variant="outline" className="group">
                <Zap className="mr-2 h-4 w-4" />
                Compare EVs
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
