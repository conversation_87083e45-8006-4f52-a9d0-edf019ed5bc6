import { EVModel } from "../types";
export interface ScrapedUrl {
    id?: number;
    url: string;
    scraped_at: string;
    success: boolean;
    error_message?: string;
}
export interface DatabaseStats {
    total_vehicles: number;
    total_scraped_urls: number;
    successful_scrapes: number;
    failed_scrapes: number;
    last_scrape_date?: string;
}
export declare class EVDatabase {
    private db;
    private dbPath;
    constructor(dbPath?: string);
    private initializeDatabase;
    /**
     * Check if a URL has already been scraped
     */
    isUrlScraped(url: string): boolean;
    /**
     * Insert or update a vehicle record
     */
    upsertVehicle(vehicle: EVModel): string;
    /**
     * Record a scraped URL
     */
    recordScrapedUrl(url: string, success: boolean, vehicleId?: string, errorMessage?: string): void;
    /**
     * Generate a unique vehicle ID
     */
    private generateVehicleId;
    /**
     * Get database statistics
     */
    getStats(): DatabaseStats;
    /**
     * Get all vehicles with optional filtering
     */
    getVehicles(filters?: {
        make?: string;
        model?: string;
        year?: number;
        minPrice?: number;
        maxPrice?: number;
        minRange?: number;
        bodyType?: string;
        limit?: number;
        offset?: number;
    }): EVModel[];
    /**
     * Export data to JSON
     */
    exportToJson(filePath: string): void;
    /**
     * Close database connection
     */
    close(): void;
}
//# sourceMappingURL=index.d.ts.map