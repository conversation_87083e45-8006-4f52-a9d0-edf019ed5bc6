@echo off
REM EV Scraper Setup Script for Windows
REM This script sets up the development environment

echo 🚗 Setting up EV Scraper...

REM Check if Node.js is installed
echo 📋 Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18.0.0 or higher.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is installed

REM Install dependencies
echo 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Install Playwright browsers
echo 🌐 Installing Playwright browsers...
call npx playwright install chromium
if %errorlevel% neq 0 (
    echo ❌ Failed to install Playwright browsers
    pause
    exit /b 1
)

REM Create environment file if it doesn't exist
if not exist .env (
    echo ⚙️  Creating environment configuration...
    copy .env.example .env
    echo ✅ Created .env file from .env.example
) else (
    echo ⚙️  Environment file already exists
)

REM Create directories
echo 📁 Creating directories...
if not exist output mkdir output
if not exist logs mkdir logs
if not exist examples\output mkdir examples\output

REM Build the project
echo 🔨 Building project...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

REM Run type checking
echo 🔍 Running type check...
call npm run type-check
if %errorlevel% neq 0 (
    echo ⚠️  Type check failed (non-critical)
)

REM Run linting
echo 🧹 Running linter...
call npm run lint
if %errorlevel% neq 0 (
    echo ⚠️  Linting issues found (non-critical)
)

echo.
echo ✅ Setup completed successfully!
echo.
echo 🚀 Quick start:
echo    npm run scrape:dev --help    # Show all options
echo    npm run scrape:dev           # Run with default settings
echo    npm run scrape:dev -- --makes "Tesla" --max-pages 2
echo.
echo 📚 Documentation:
echo    type README.md               # Full documentation
echo    dir examples\                # Usage examples
echo.
echo 🔧 Configuration:
echo    notepad .env                 # Environment variables
echo    notepad src\utils\config.ts  # Advanced configuration
echo.

pause
