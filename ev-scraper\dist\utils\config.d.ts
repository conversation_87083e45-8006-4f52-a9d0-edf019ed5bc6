import { ScrapingConfig } from "../types";
/**
 * Default scraping configuration
 * Can be overridden by environment variables or command line arguments
 */
export declare const defaultConfig: ScrapingConfig;
/**
 * Target website configuration
 */
export declare const siteConfig: {
    baseUrl: string;
    robotsTxtUrl: string;
    urls: {
        carList: string;
        carDetail: (id: string) => string;
        search: string;
        brands: string;
    };
    selectors: {
        carLinks: string;
        carCards: string;
        pagination: string;
        nextPage: string;
        carTitle: string;
        specifications: string;
        specTable: string;
        specRows: string;
        specLabel: string;
        specValue: string;
        price: string;
        range: string;
        battery: string;
        charging: string;
        performance: string;
        efficiency: string;
        mainImage: string;
        galleryImages: string;
        bodyType: string;
        drivetrain: string;
        seating: string;
    };
    patterns: {
        price: RegExp;
        range: RegExp;
        battery: RegExp;
        charging: RegExp;
        acceleration: RegExp;
        speed: RegExp;
        efficiency: RegExp;
        year: RegExp;
        power: RegExp;
    };
    headers: {
        Accept: string;
        "Accept-Language": string;
        "Accept-Encoding": string;
        DNT: string;
        Connection: string;
        "Upgrade-Insecure-Requests": string;
        "Sec-Fetch-Dest": string;
        "Sec-Fetch-Mode": string;
        "Sec-Fetch-Site": string;
        "Cache-Control": string;
    };
};
/**
 * Validation rules for scraped data
 */
export declare const validationRules: {
    required_fields: string[];
    ranges: {
        year: {
            min: number;
            max: number;
        };
        price: {
            min: number;
            max: number;
        };
        battery_capacity: {
            min: number;
            max: number;
        };
        range_miles: {
            min: number;
            max: number;
        };
        charging_speed: {
            min: number;
            max: number;
        };
        acceleration: {
            min: number;
            max: number;
        };
        top_speed: {
            min: number;
            max: number;
        };
        efficiency: {
            min: number;
            max: number;
        };
    };
    valid_body_types: string[];
    valid_drivetrains: string[];
    valid_production_status: string[];
};
/**
 * Output file naming conventions
 */
export declare const outputConfig: {
    fileNames: {
        json: (timestamp: string) => string;
        csv: (timestamp: string) => string;
        sql: (timestamp: string) => string;
        errors: (timestamp: string) => string;
        summary: (timestamp: string) => string;
    };
    timestamp: () => string;
};
//# sourceMappingURL=config.d.ts.map