import Database from "better-sqlite3";
import path from "path";
import fs from "fs";
import { EVModel } from "../types";
import { logger } from "../utils/logger";

export interface ScrapedUrl {
  id?: number;
  url: string;
  scraped_at: string;
  success: boolean;
  error_message?: string;
}

export interface DatabaseStats {
  total_vehicles: number;
  total_scraped_urls: number;
  successful_scrapes: number;
  failed_scrapes: number;
  last_scrape_date?: string;
}

export class EVDatabase {
  private db: Database.Database;
  private dbPath: string;

  constructor(dbPath?: string) {
    this.dbPath = dbPath || path.join(process.cwd(), "data", "ev-scraper.db");

    // Ensure data directory exists
    const dataDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    this.db = new Database(this.dbPath);
    this.initializeDatabase();

    logger.info("Database initialized", { path: this.dbPath });
  }

  private initializeDatabase(): void {
    // Enable foreign keys
    this.db.pragma("foreign_keys = ON");

    // Create vehicles table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS vehicles (
        id TEXT PRIMARY KEY,
        make TEXT NOT NULL,
        model TEXT NOT NULL,
        year INTEGER NOT NULL,
        trim TEXT,
        variant TEXT,
        
        -- Pricing
        price_msrp INTEGER, -- in cents
        price_base INTEGER,
        price_including_incentives INTEGER,
        currency TEXT DEFAULT 'USD',
        
        -- Battery & Range
        battery_capacity_kwh REAL NOT NULL,
        battery_usable_kwh REAL,
        range_epa_miles REAL,
        range_wltp_km REAL,
        range_real_world_miles REAL,
        
        -- Charging
        charging_speed_ac_kw REAL,
        charging_speed_dc_kw REAL,
        charging_time_10_80_minutes INTEGER,
        charging_time_0_100_minutes INTEGER,
        charging_port_type TEXT,
        
        -- Performance
        acceleration_0_60_mph REAL,
        acceleration_0_100_kmh REAL,
        top_speed_mph REAL,
        top_speed_kmh REAL,
        
        -- Efficiency
        efficiency_mpge REAL,
        efficiency_kwh_100km REAL,
        efficiency_miles_kwh REAL,

        -- Physical
        length_mm REAL,
        width_mm REAL,
        height_mm REAL,
        wheelbase_mm REAL,
        weight_kg REAL,
        cargo_space_cu_ft REAL,
        cargo_space_liters REAL,
        seating_capacity INTEGER,
        doors INTEGER,
        
        -- Classification
        body_type TEXT,
        drivetrain TEXT CHECK (drivetrain IN ('FWD', 'RWD', 'AWD', '4WD')),

        -- Status
        production_status TEXT,
        availability_region TEXT, -- JSON array of regions

        -- Motor specifications
        motor_power_kw REAL,
        motor_power_hp REAL,
        motor_torque_nm REAL,
        
        -- Metadata
        source_url TEXT NOT NULL,
        scraped_at TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create scraped_urls table for duplicate prevention
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS scraped_urls (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        url TEXT UNIQUE NOT NULL,
        scraped_at TEXT NOT NULL,
        success BOOLEAN NOT NULL,
        error_message TEXT,
        vehicle_id TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vehicle_id) REFERENCES vehicles (id)
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_vehicles_make_model ON vehicles (make, model);
      CREATE INDEX IF NOT EXISTS idx_vehicles_year ON vehicles (year);
      CREATE INDEX IF NOT EXISTS idx_vehicles_price ON vehicles (price_msrp);
      CREATE INDEX IF NOT EXISTS idx_vehicles_range ON vehicles (range_epa_miles);
      CREATE INDEX IF NOT EXISTS idx_vehicles_body_type ON vehicles (body_type);
      CREATE INDEX IF NOT EXISTS idx_scraped_urls_url ON scraped_urls (url);
      CREATE INDEX IF NOT EXISTS idx_scraped_urls_success ON scraped_urls (success);
    `);

    logger.info("Database schema initialized");
  }

  /**
   * Check if a URL has already been scraped
   */
  isUrlScraped(url: string): boolean {
    const stmt = this.db.prepare(
      "SELECT 1 FROM scraped_urls WHERE url = ? LIMIT 1"
    );
    const result = stmt.get(url);
    return !!result;
  }

  /**
   * Insert or update a vehicle record
   */
  upsertVehicle(vehicle: EVModel): string {
    const vehicleId = vehicle.id || this.generateVehicleId(vehicle);

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO vehicles (
        id, make, model, year, trim, variant,
        price_msrp, price_base, price_including_incentives, currency,
        battery_capacity_kwh, battery_usable_kwh, range_epa_miles, range_wltp_km, range_real_world_miles,
        charging_speed_ac_kw, charging_speed_dc_kw, charging_time_10_80_minutes, charging_time_0_100_minutes, charging_port_type,
        acceleration_0_60_mph, acceleration_0_100_kmh, top_speed_mph, top_speed_kmh,
        efficiency_mpge, efficiency_kwh_100km, efficiency_miles_kwh,
        length_mm, width_mm, height_mm, wheelbase_mm, weight_kg,
        cargo_space_cu_ft, cargo_space_liters, seating_capacity, doors,
        body_type, drivetrain, production_status, availability_region,
        motor_power_kw, motor_power_hp, motor_torque_nm,
        source_url, scraped_at, updated_at
      ) VALUES (
        ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?,
        ?, ?, CURRENT_TIMESTAMP
      )
    `);

    try {
      stmt.run(
        vehicleId,
        vehicle.make,
        vehicle.model,
        vehicle.year,
        vehicle.trim,
        vehicle.variant,
        vehicle.price_msrp,
        vehicle.price_base,
        vehicle.price_including_incentives,
        vehicle.currency,
        vehicle.battery_capacity_kwh,
        vehicle.battery_usable_kwh,
        vehicle.range_epa_miles,
        vehicle.range_wltp_km,
        vehicle.range_real_world_miles,
        vehicle.charging_speed_ac_kw,
        vehicle.charging_speed_dc_kw,
        vehicle.charging_time_10_80_minutes,
        vehicle.charging_time_0_100_minutes,
        vehicle.charging_port_type,
        vehicle.acceleration_0_60_mph,
        vehicle.acceleration_0_100_kmh,
        vehicle.top_speed_mph,
        vehicle.top_speed_kmh,
        vehicle.efficiency_mpge,
        vehicle.efficiency_kwh_100km,
        vehicle.efficiency_miles_kwh,
        vehicle.length_mm,
        vehicle.width_mm,
        vehicle.height_mm,
        vehicle.wheelbase_mm,
        vehicle.weight_kg,
        vehicle.cargo_space_cu_ft,
        vehicle.cargo_space_liters,
        vehicle.seating_capacity,
        vehicle.doors,
        vehicle.body_type,
        vehicle.drivetrain,
        vehicle.production_status,
        vehicle.availability_region
          ? JSON.stringify(vehicle.availability_region)
          : null,
        vehicle.motor_power_kw,
        vehicle.motor_power_hp,
        vehicle.motor_torque_nm,
        vehicle.source_url,
        vehicle.scraped_at
      );

      logger.debug("Vehicle upserted", {
        id: vehicleId,
        make: vehicle.make,
        model: vehicle.model,
      });
      return vehicleId;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error("Failed to upsert vehicle", {
        vehicleId,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Record a scraped URL
   */
  recordScrapedUrl(
    url: string,
    success: boolean,
    vehicleId?: string,
    errorMessage?: string
  ): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO scraped_urls (url, scraped_at, success, vehicle_id, error_message)
      VALUES (?, ?, ?, ?, ?)
    `);

    try {
      stmt.run(
        url,
        new Date().toISOString(),
        success ? 1 : 0, // Convert boolean to integer for SQLite
        vehicleId || null,
        errorMessage || null
      );
      logger.debug("Scraped URL recorded", { url, success, vehicleId });
    } catch (error) {
      const err = error instanceof Error ? error.message : String(error);
      logger.error("Failed to record scraped URL", { url, error: err });
    }
  }

  /**
   * Generate a unique vehicle ID
   */
  private generateVehicleId(vehicle: EVModel): string {
    const makeModel = `${vehicle.make}-${vehicle.model}`
      .toLowerCase()
      .replace(/\s+/g, "-");
    const year = vehicle.year;
    const trim = vehicle.trim
      ? `-${vehicle.trim.toLowerCase().replace(/\s+/g, "-")}`
      : "";
    return `${makeModel}-${year}${trim}`;
  }

  /**
   * Get database statistics
   */
  getStats(): DatabaseStats {
    const vehicleCount = this.db
      .prepare("SELECT COUNT(*) as count FROM vehicles")
      .get() as { count: number };
    const urlCount = this.db
      .prepare("SELECT COUNT(*) as count FROM scraped_urls")
      .get() as { count: number };
    const successCount = this.db
      .prepare("SELECT COUNT(*) as count FROM scraped_urls WHERE success = 1")
      .get() as { count: number };
    const failCount = this.db
      .prepare("SELECT COUNT(*) as count FROM scraped_urls WHERE success = 0")
      .get() as { count: number };
    const lastScrape = this.db
      .prepare("SELECT MAX(scraped_at) as last_date FROM scraped_urls")
      .get() as { last_date: string | null };

    return {
      total_vehicles: vehicleCount.count,
      total_scraped_urls: urlCount.count,
      successful_scrapes: successCount.count,
      failed_scrapes: failCount.count,
      last_scrape_date: lastScrape.last_date || undefined,
    };
  }

  /**
   * Get all vehicles with optional filtering
   */
  getVehicles(filters?: {
    make?: string;
    model?: string;
    year?: number;
    minPrice?: number;
    maxPrice?: number;
    minRange?: number;
    bodyType?: string;
    limit?: number;
    offset?: number;
  }): EVModel[] {
    let query = "SELECT * FROM vehicles WHERE 1=1";
    const params: any[] = [];

    if (filters?.make) {
      query += " AND make = ?";
      params.push(filters.make);
    }
    if (filters?.model) {
      query += " AND model LIKE ?";
      params.push(`%${filters.model}%`);
    }
    if (filters?.year) {
      query += " AND year = ?";
      params.push(filters.year);
    }
    if (filters?.minPrice) {
      query += " AND price_msrp >= ?";
      params.push(filters.minPrice);
    }
    if (filters?.maxPrice) {
      query += " AND price_msrp <= ?";
      params.push(filters.maxPrice);
    }
    if (filters?.minRange) {
      query += " AND range_epa_miles >= ?";
      params.push(filters.minRange);
    }
    if (filters?.bodyType) {
      query += " AND body_type = ?";
      params.push(filters.bodyType);
    }

    query += " ORDER BY make, model, year";

    if (filters?.limit) {
      query += " LIMIT ?";
      params.push(filters.limit);
      if (filters?.offset) {
        query += " OFFSET ?";
        params.push(filters.offset);
      }
    }

    const stmt = this.db.prepare(query);
    return stmt.all(...params) as EVModel[];
  }

  /**
   * Export data to JSON
   */
  exportToJson(filePath: string): void {
    const vehicles = this.getVehicles();
    const stats = this.getStats();

    const exportData = {
      metadata: {
        exported_at: new Date().toISOString(),
        vehicle_count: vehicles.length,
        ...stats,
      },
      vehicles,
    };

    fs.writeFileSync(filePath, JSON.stringify(exportData, null, 2));
    logger.info("Data exported to JSON", {
      filePath,
      vehicleCount: vehicles.length,
    });
  }

  /**
   * Close database connection
   */
  close(): void {
    this.db.close();
    logger.info("Database connection closed");
  }
}
