# EV Database Scraper

A comprehensive web scraper for extracting electric vehicle data from [ev-database.org](https://ev-database.org/). This tool extracts detailed EV specifications, pricing, performance metrics, and technical data in multiple output formats.

## 🚗 Features

- **Comprehensive Data Extraction**: Battery capacity, range, charging speeds, performance metrics, pricing, and more
- **Multiple Output Formats**: JSON, CSV, and SQL insert statements
- **Respectful Scraping**: Rate limiting, robots.txt compliance, and retry logic
- **Robust Error Handling**: Automatic retries, validation, and detailed error reporting
- **Progress Tracking**: Real-time progress updates and detailed logging
- **Configurable**: Extensive CLI options and environment variable support
- **TypeScript**: Full type safety and modern JavaScript features

## 📋 Requirements

- Node.js 18.0.0 or higher
- npm or yarn package manager
- At least 2GB RAM (for browser automation)
- Stable internet connection

## 🚀 Quick Start

### Installation

```bash
# Clone or copy the ev-scraper directory
cd ev-scraper

# Install dependencies
npm install

# Install Playwright browsers
npx playwright install chromium

# Copy environment configuration
cp .env.example .env

# Build the project
npm run build
```

### Basic Usage

```bash
# Run with default settings
npm run scrape

# Or use the development version
npm run scrape:dev

# Run with custom options
npm run scrape -- --makes "Tesla,BMW" --max-pages 5 --delay 3000
```

## 🛠️ Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Rate limiting (be respectful!)
DELAY_BETWEEN_REQUESTS=2000    # 2 seconds between requests
MAX_CONCURRENT_REQUESTS=3      # Max 3 concurrent requests

# Browser settings
HEADLESS=true                  # Run browser in headless mode
VIEWPORT_WIDTH=1920           # Browser viewport width
VIEWPORT_HEIGHT=1080          # Browser viewport height

# Output settings
OUTPUT_DIR=./output           # Output directory
SAVE_JSON=true               # Save as JSON
SAVE_CSV=true                # Save as CSV
SAVE_SQL=true                # Save as SQL

# Logging
LOG_LEVEL=info               # Log level (debug, info, warn, error)
```

### CLI Options

```bash
Options:
  -o, --output <dir>           Output directory for scraped data
  -d, --delay <ms>            Delay between requests in milliseconds
  -c, --concurrent <num>      Maximum concurrent requests
  -r, --retries <num>         Maximum retry attempts
  --headless                  Run browser in headless mode
  -p, --max-pages <num>       Maximum pages to scrape
  -m, --makes <list>          Comma-separated list of makes (e.g., "Tesla,BMW")
  --year-start <year>         Start year for filtering
  --year-end <year>           End year for filtering
  --json                      Save data as JSON
  --csv                       Save data as CSV
  --sql                       Save data as SQL
  --check-robots              Check robots.txt before scraping
  -v, --verbose               Enable verbose logging
  -h, --help                  Show help
```

## 📊 Data Schema

### EV Model Data Structure

```typescript
interface EVModel {
  // Basic Information
  make: string                    // e.g., "Tesla"
  model: string                   // e.g., "Model 3"
  year: number                    // e.g., 2024
  trim?: string                   // e.g., "Long Range"
  
  // Pricing (in cents)
  price_msrp?: number            // e.g., 4099900 for $40,999
  
  // Battery & Range
  battery_capacity_kwh: number   // e.g., 75.0
  range_epa_miles?: number       // e.g., 358
  
  // Charging
  charging_speed_dc_kw?: number  // e.g., 250
  charging_speed_ac_kw?: number  // e.g., 11
  
  // Performance
  acceleration_0_60_mph?: number // e.g., 4.2
  top_speed_mph?: number         // e.g., 162
  efficiency_mpge?: number       // e.g., 132
  
  // Physical Specs
  body_type?: string             // e.g., "sedan"
  seating_capacity?: number      // e.g., 5
  drivetrain?: string            // e.g., "RWD"
  
  // Metadata
  production_status?: string     // "current" | "upcoming" | "discontinued"
  images?: string[]              // Array of image URLs
  source_url?: string            // Original page URL
  scraped_at?: string            // ISO timestamp
}
```

## 📁 Output Files

The scraper generates multiple output files in the specified directory:

```
output/
├── ev-data-2024-01-15T10-30-00.json     # Complete data in JSON format
├── ev-data-2024-01-15T10-30-00.csv      # Spreadsheet-compatible CSV
├── ev-data-2024-01-15T10-30-00.sql      # SQL INSERT statements
├── scraping-errors-2024-01-15T10-30-00.json  # Error details (if any)
└── scraping-summary-2024-01-15T10-30-00.json # Execution summary
```

### JSON Output

```json
{
  "metadata": {
    "scraped_at": "2024-01-15T10:30:00.000Z",
    "total_vehicles": 150,
    "version": "1.0.0"
  },
  "vehicles": [
    {
      "make": "Tesla",
      "model": "Model 3",
      "year": 2024,
      "price_msrp": 4099900,
      "battery_capacity_kwh": 75,
      "range_epa_miles": 358,
      // ... more fields
    }
  ]
}
```

### SQL Output

The SQL output is compatible with the existing EV database schema:

```sql
-- Electric Vehicle Data Import
-- Generated on: 2024-01-15T10:30:00.000Z
-- Total records: 150

BEGIN TRANSACTION;

INSERT INTO ev_models (make, model, year, battery_capacity_kwh, range_epa_miles, price_msrp) 
VALUES ('Tesla', 'Model 3', 2024, 75.0, 358, 4099900);

-- ... more INSERT statements

COMMIT;
```

## 🔧 Usage Examples

### Basic Scraping

```bash
# Scrape all available EVs with default settings
npm run scrape

# Scrape with verbose logging
npm run scrape -- --verbose
```

### Filtered Scraping

```bash
# Scrape only Tesla and BMW models
npm run scrape -- --makes "Tesla,BMW"

# Scrape only recent models (2022-2024)
npm run scrape -- --year-start 2022 --year-end 2024

# Limit to first 10 pages
npm run scrape -- --max-pages 10
```

### Custom Output

```bash
# Save only JSON format to custom directory
npm run scrape -- --output ./my-data --json --no-csv --no-sql

# Save all formats with custom delay
npm run scrape -- --delay 5000 --output ./ev-data-slow
```

### Conservative Scraping

```bash
# Very respectful scraping (slow but safe)
npm run scrape -- --delay 10000 --concurrent 1 --retries 5
```

## 🔍 Monitoring and Debugging

### Logs

Logs are automatically saved to the `logs/` directory:

- `combined.log` - All log messages
- `error.log` - Error messages only
- `scraping.log` - Detailed scraping progress

### Progress Tracking

The scraper provides real-time progress updates:

```
[10:30:15] [info]: Progress update {
  "url": "https://ev-database.org/car/tesla-model-3",
  "success": true,
  "progress": "15.3%",
  "processed": 23,
  "total": 150,
  "successful": 22,
  "failed": 1,
  "elapsedMs": 45000,
  "estimatedRemainingMs": 249000
}
```

### Error Handling

Failed scrapes are automatically retried and logged:

```json
{
  "url": "https://ev-database.org/car/problematic-model",
  "error_message": "Page content too short or empty",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "retry_count": 3
}
```

## ⚙️ Advanced Configuration

### Custom Selectors

Modify `src/utils/config.ts` to adjust CSS selectors for data extraction:

```typescript
selectors: {
  carTitle: 'h1, .car-title, .vehicle-name',
  price: '.price, .msrp, .starting-price',
  range: '.range, .epa-range, .wltp-range',
  // ... more selectors
}
```

### Data Validation

Customize validation rules in `src/utils/validation.ts`:

```typescript
ranges: {
  year: { min: 2010, max: 2026 },
  price: { min: 1000, max: 500000 },
  battery_capacity: { min: 10, max: 300 },
  // ... more validation rules
}
```

## 🚨 Important Notes

### Ethical Scraping

- **Respect robots.txt**: The scraper checks robots.txt by default
- **Rate limiting**: Default 2-second delay between requests
- **Reasonable concurrency**: Max 3 concurrent requests by default
- **Error handling**: Automatic retries with exponential backoff

### Legal Considerations

- This tool is for educational and research purposes
- Ensure compliance with the website's terms of service
- Consider reaching out to the website owner for permission
- Use scraped data responsibly and ethically

### Performance Tips

- Use `--headless` for faster scraping (default)
- Increase `--delay` if you encounter rate limiting
- Reduce `--concurrent` if you experience timeouts
- Use `--max-pages` to limit scope for testing

## 🐛 Troubleshooting

### Common Issues

**Browser not found:**
```bash
npx playwright install chromium
```

**Memory issues:**
```bash
# Reduce concurrency
npm run scrape -- --concurrent 1
```

**Rate limiting:**
```bash
# Increase delay between requests
npm run scrape -- --delay 5000
```

**Validation errors:**
```bash
# Check logs for specific validation failures
tail -f logs/scraping.log
```

### Debug Mode

```bash
# Enable debug logging
npm run scrape -- --verbose

# Run in non-headless mode to see browser
npm run scrape -- --no-headless --verbose
```

## 📈 Performance Metrics

Typical performance on a modern machine:

- **Speed**: ~50-100 vehicles per hour (with default settings)
- **Success Rate**: 85-95% (depending on website stability)
- **Memory Usage**: ~500MB-1GB
- **CPU Usage**: Moderate (browser automation)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For issues and questions:

1. Check the troubleshooting section
2. Review the logs in the `logs/` directory
3. Create an issue with detailed error information
4. Include your configuration and command used

---

**Happy Scraping! 🚗⚡**
